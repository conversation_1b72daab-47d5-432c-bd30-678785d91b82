<?php

use App\Http\Controllers\ClientController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\PaymentController;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */
Route::get('/seed', function () {
    Artisan::call("db:seed --class=ResetPermissionSeeder");
});



// Authentication Routes (Jetstream automatically registers these)
// But we need to define the dashboard route
Route::middleware(['auth:sanctum', 'verified'])->get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Language switching routes (outside middleware group to avoid conflicts)
Route::get('/language/{locale}', [App\Http\Controllers\LanguageController::class, 'switch'])->name('language.switch');
Route::get('/api/language/current', [App\Http\Controllers\LanguageController::class, 'getCurrentLanguage'])->name('language.current');
Route::get('/api/language/available', [App\Http\Controllers\LanguageController::class, 'getAvailableLanguages'])->name('language.available');

Route::group(['middleware' => ['web', 'locale']], function () {
    // home page
    Route::get('/', function () {
        $stats = [
            'completed_journeys' => 1200,
            'satisfied_clients' => 350,
            'years_experience' => 8,
        ];
        return view('welcome', compact('stats'));
    })->name('home');
    
    // client registration
    Route::get('/clients/register', [ClientController::class, 'create'])->name('clients.create');
    Route::post('/clients/register', [ClientController::class, 'store'])->name('clients.store');
    Route::get('/thankyou', function () {
        return view('clients.thankyou');
    })->name('clients.thankyou');
    
    // Contact form submission
    Route::post('/contact', function () {
        // You can handle the contact form logic here or point to a controller
        // For now, just redirect back with a success message
        return back()->with('success', __('messages.contact_form_sent') ?? 'Your message has been sent!');
    })->name('contact.submit');
    
    // Test translation route
    Route::get('/test-translation', function () {
        return __('welcome_to_royal_transit');
    });
    
    // Test translation route for array-based translation
    Route::get('/test-translation-array', function () {
        return __('messages.welcome_to_royal_transit');
    });
    
    // Admin routes with enhanced security
    Route::middleware(['auth', 'verified', 'admin', 'rate.limit:admin,120,1'])->prefix('admin')->name('admin.')->group(function () {
        // Dashboard - accessible to all admin users
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

        // Client Management Routes
        Route::middleware(['permission:read admin_clients'])->group(function () {
            Route::get('/clients', [\App\Http\Controllers\Admin\AdminClientController::class, 'index'])->name('clients.index');
        });

        Route::middleware(['permission:create admin_clients'])->group(function () {
            Route::get('/clients/create', [\App\Http\Controllers\Admin\AdminClientController::class, 'create'])->name('clients.create');
            Route::post('/clients', [\App\Http\Controllers\Admin\AdminClientController::class, 'store'])->name('clients.store');
        });

        Route::middleware(['permission:read admin_clients'])->group(function () {
            Route::get('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'show'])->name('clients.show');
        });

        Route::middleware(['permission:update admin_clients'])->group(function () {
            Route::get('/clients/{client}/edit', [\App\Http\Controllers\Admin\AdminClientController::class, 'edit'])->name('clients.edit');
            Route::put('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'update'])->name('clients.update');
        });

        Route::middleware(['permission:delete admin_clients'])->group(function () {
            Route::delete('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'destroy'])->name('clients.destroy');
        });

        // Route Management Routes
        Route::middleware(['permission:read admin_routes'])->group(function () {
            Route::get('/routes', [\App\Http\Controllers\Admin\AdminRouteController::class, 'index'])->name('routes.index');
        });

        Route::middleware(['permission:create admin_routes'])->group(function () {
            Route::get('/routes/create', [\App\Http\Controllers\Admin\AdminRouteController::class, 'create'])->name('routes.create');
            Route::post('/routes', [\App\Http\Controllers\Admin\AdminRouteController::class, 'store'])->name('routes.store');
        });

        Route::middleware(['permission:read admin_routes'])->group(function () {
            Route::get('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'show'])->name('routes.show');
        });

        Route::middleware(['permission:update admin_routes'])->group(function () {
            Route::get('/routes/{route}/edit', [\App\Http\Controllers\Admin\AdminRouteController::class, 'edit'])->name('routes.edit');
            Route::put('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'update'])->name('routes.update');
        });

        Route::middleware(['permission:delete admin_routes'])->group(function () {
            Route::delete('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'destroy'])->name('routes.destroy');
        });

        // Driver Management Routes
        Route::middleware(['permission:read admin_drivers'])->group(function () {
            Route::get('/drivers', [\App\Http\Controllers\Admin\AdminDriverController::class, 'index'])->name('drivers.index');
            Route::get('/drivers/expiring-licenses', [\App\Http\Controllers\Admin\AdminDriverController::class, 'expiringLicenses'])->name('drivers.expiring-licenses');
        });

        Route::middleware(['permission:create admin_drivers'])->group(function () {
            Route::get('/drivers/create', [\App\Http\Controllers\Admin\AdminDriverController::class, 'create'])->name('drivers.create');
            Route::post('/drivers', [\App\Http\Controllers\Admin\AdminDriverController::class, 'store'])->name('drivers.store');
        });

        Route::middleware(['permission:read admin_drivers'])->group(function () {
            Route::get('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'show'])->name('drivers.show');
        });

        Route::middleware(['permission:update admin_drivers'])->group(function () {
            Route::get('/drivers/{driver}/edit', [\App\Http\Controllers\Admin\AdminDriverController::class, 'edit'])->name('drivers.edit');
            Route::put('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'update'])->name('drivers.update');
            Route::post('/drivers/{driver}/assign-route', [\App\Http\Controllers\Admin\AdminDriverController::class, 'assignRoute'])->name('drivers.assign-route');
            Route::post('/drivers/{driver}/remove-route', [\App\Http\Controllers\Admin\AdminDriverController::class, 'removeRoute'])->name('drivers.remove-route');
            Route::post('/drivers/{driver}/suspend', [\App\Http\Controllers\Admin\AdminDriverController::class, 'suspend'])->name('drivers.suspend');
            Route::post('/drivers/{driver}/activate', [\App\Http\Controllers\Admin\AdminDriverController::class, 'activate'])->name('drivers.activate');
            Route::post('/drivers/{driver}/routes/{route}/remove', [\App\Http\Controllers\Admin\AdminDriverController::class, 'removeFromSpecificRoute'])->name('drivers.routes.remove');
        });

        Route::middleware(['permission:delete admin_drivers'])->group(function () {
            Route::delete('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'destroy'])->name('drivers.destroy');
        });

        // Financial Management Routes
        Route::middleware(['permission:view admin_financial'])->group(function () {
            Route::get('/financial', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'index'])->name('financial.index');
            Route::get('/financial/reports', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'reports'])->name('financial.reports');
            Route::get('/financial/revenue', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'revenue'])->name('financial.revenue');
            Route::get('/financial/payments', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'payments'])->name('financial.payments');
        });

        Route::middleware(['permission:update admin_financial'])->group(function () {
            Route::post('/financial/payments/{payment}/mark-paid', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'markPaid'])->name('financial.payments.mark-paid');
            Route::post('/financial/payments/{payment}/mark-overdue', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'markOverdue'])->name('financial.payments.mark-overdue');
        });

        // DataTable Routes (AJAX endpoints)
        Route::middleware(['permission:read admin_clients'])->group(function () {
            Route::get('/clients-data', [\App\DataTables\AdminClientsDataTable::class, 'ajax'])->name('clients.data');
        });

        Route::middleware(['permission:read admin_routes'])->group(function () {
            Route::get('/routes-data', [\App\DataTables\AdminRoutesDataTable::class, 'ajax'])->name('routes.data');
        });

        Route::middleware(['permission:read admin_drivers'])->group(function () {
            Route::get('/drivers-data', [\App\DataTables\AdminDriversDataTable::class, 'ajax'])->name('drivers.data');
        });

        // Widget/AJAX Routes
        Route::middleware(['permission:read admin_routes'])->group(function () {
            Route::get('/routes/capacity-report', [\App\Http\Controllers\Admin\AdminRouteController::class, 'showCapacityReport'])->name('routes.capacity-report');
            Route::get('/routes/driver-assignments', [\App\Http\Controllers\Admin\AdminRouteController::class, 'showDriverAssignments'])->name('routes.driver-assignments');
            Route::get('/routes/schedule-overview', [\App\Http\Controllers\Admin\AdminRouteController::class, 'showScheduleOverview'])->name('routes.schedule-overview');
        });

        Route::middleware(['permission:read admin_drivers'])->group(function () {
            Route::get('/drivers/unassigned', [\App\Http\Controllers\Admin\AdminDriverController::class, 'showUnassignedDrivers'])->name('drivers.unassigned');
            Route::get('/drivers/performance', [\App\Http\Controllers\Admin\AdminDriverController::class, 'showDriverPerformance'])->name('drivers.performance');
        });

        // User Management Routes
        Route::middleware(['permission:read users'])->group(function () {
            Route::get('/users', [\App\Http\Controllers\Admin\UserController::class, 'index'])->name('users.index');
            Route::get('/users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'show'])->name('users.show');
        });

        Route::middleware(['permission:create users', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/users/create', [\App\Http\Controllers\Admin\UserController::class, 'create'])->name('users.create');
            Route::post('/users', [\App\Http\Controllers\Admin\UserController::class, 'store'])->name('users.store');
        });

        Route::middleware(['permission:update users', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/users/{user}/edit', [\App\Http\Controllers\Admin\UserController::class, 'edit'])->name('users.edit');
            Route::put('/users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'update'])->name('users.update');
            Route::post('/users/{user}/restore', [\App\Http\Controllers\Admin\UserController::class, 'restore'])->name('users.restore');
            Route::post('/users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');
            Route::post('/users/bulk-action', [\App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('users.bulk-action');
        });

        Route::middleware(['permission:delete users', 'rate.limit:sensitive,5,1'])->group(function () {
            Route::delete('/users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('users.destroy');
            Route::delete('/users/{user}/force', [\App\Http\Controllers\Admin\UserController::class, 'forceDelete'])->name('users.force-delete');
        });

        // Role Management Routes
        Route::middleware(['permission:read roles'])->group(function () {
            Route::get('/roles', [\App\Http\Controllers\Admin\RoleController::class, 'index'])->name('roles.index');
            Route::get('/roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'show'])->name('roles.show');
        });

        Route::middleware(['permission:create roles', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/roles/create', [\App\Http\Controllers\Admin\RoleController::class, 'create'])->name('roles.create');
            Route::post('/roles', [\App\Http\Controllers\Admin\RoleController::class, 'store'])->name('roles.store');
            Route::post('/roles/{role}/clone', [\App\Http\Controllers\Admin\RoleController::class, 'clone'])->name('roles.clone');
        });

        Route::middleware(['permission:update roles', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/roles/{role}/edit', [\App\Http\Controllers\Admin\RoleController::class, 'edit'])->name('roles.edit');
            Route::put('/roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'update'])->name('roles.update');
        });

        Route::middleware(['permission:delete roles', 'rate.limit:sensitive,5,1'])->group(function () {
            Route::delete('/roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'destroy'])->name('roles.destroy');
        });

        // Permission Management Routes
        Route::middleware(['permission:read permissions'])->group(function () {
            Route::get('/permissions', [\App\Http\Controllers\Admin\PermissionController::class, 'index'])->name('permissions.index');
            Route::get('/permissions/{permission}', [\App\Http\Controllers\Admin\PermissionController::class, 'show'])->name('permissions.show');
        });

        Route::middleware(['permission:create permissions', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/permissions/create', [\App\Http\Controllers\Admin\PermissionController::class, 'create'])->name('permissions.create');
            Route::post('/permissions', [\App\Http\Controllers\Admin\PermissionController::class, 'store'])->name('permissions.store');
            Route::post('/permissions/bulk-create', [\App\Http\Controllers\Admin\PermissionController::class, 'bulkCreate'])->name('permissions.bulk-create');
        });

        Route::middleware(['permission:update permissions', 'rate.limit:sensitive,10,1'])->group(function () {
            Route::get('/permissions/{permission}/edit', [\App\Http\Controllers\Admin\PermissionController::class, 'edit'])->name('permissions.edit');
            Route::put('/permissions/{permission}', [\App\Http\Controllers\Admin\PermissionController::class, 'update'])->name('permissions.update');
            Route::post('/permissions/sync-roles', [\App\Http\Controllers\Admin\PermissionController::class, 'syncWithRoles'])->name('permissions.sync-roles');
        });

        Route::middleware(['permission:delete permissions', 'rate.limit:sensitive,5,1'])->group(function () {
            Route::delete('/permissions/{permission}', [\App\Http\Controllers\Admin\PermissionController::class, 'destroy'])->name('permissions.destroy');
        });

        // Translation Management Routes
        Route::middleware(['permission:read translations'])->group(function () {
            Route::get('/translations', [\App\Http\Controllers\Admin\TranslationController::class, 'index'])->name('translations.index');
        });

        Route::middleware(['permission:create translations'])->group(function () {
            Route::get('/translations/create', [\App\Http\Controllers\Admin\TranslationController::class, 'create'])->name('translations.create');
            Route::post('/translations', [\App\Http\Controllers\Admin\TranslationController::class, 'store'])->name('translations.store');
        });

        Route::middleware(['permission:update translations'])->group(function () {
            Route::get('/translations/{key}/edit', [\App\Http\Controllers\Admin\TranslationController::class, 'edit'])->name('translations.edit');
            Route::put('/translations/{key}', [\App\Http\Controllers\Admin\TranslationController::class, 'update'])->name('translations.update');
            Route::post('/translations/sync', [\App\Http\Controllers\Admin\TranslationController::class, 'sync'])->name('translations.sync');
        });

        Route::middleware(['permission:delete translations'])->group(function () {
            Route::delete('/translations/{key}', [\App\Http\Controllers\Admin\TranslationController::class, 'destroy'])->name('translations.destroy');
        });

        // Legacy routes (keeping for backward compatibility)
        Route::middleware(['permission:view admin_subscriptions'])->group(function () {
            Route::resource('subscriptions', SubscriptionController::class);
        });

        Route::middleware(['permission:view admin_payments'])->group(function () {
            Route::resource('payments', PaymentController::class);
        });
    });
});
