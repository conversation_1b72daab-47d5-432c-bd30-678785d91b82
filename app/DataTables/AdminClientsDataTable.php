<?php
namespace App\DataTables;

use App\Models\Client;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AdminClientsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', function ($row) {
                $actions = '';

                if (auth()->user()->can('read admin_clients')) {
                    $actions .= '<a href="' . route('admin.clients.show', $row->id) . '" class="btn btn-sm btn-info me-1" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';
                }

                if (auth()->user()->can('update admin_clients')) {
                    $actions .= '<a href="' . route('admin.clients.edit', $row->id) . '" class="btn btn-sm btn-warning me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                }

                if (auth()->user()->can('delete admin_clients')) {
                    $actions .= '<form method="POST" action="' . route('admin.clients.destroy', $row->id) . '" style="display: inline;" onsubmit="return confirm(\'Are you sure?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>';
                }

                return $actions;
            })
            ->editColumn('name', function ($row) {
                return $row->user->name ?? 'N/A';
            })
            ->editColumn('email', function ($row) {
                return $row->user->email ?? 'N/A';
            })
            ->editColumn('phone', function ($row) {
                return $row->user->phone ?? 'N/A';
            })
            ->addColumn('students_count', function ($row) {
                return $row->students->count();
            })
            ->addColumn('active_subscriptions', function ($row) {
                return $row->activeSubscriptions->count();
            })
            ->editColumn('area', function ($row) {
                return $row->area ?? 'N/A';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at->format('Y-m-d H:i');
            })
            ->rawColumns(['actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Client $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Client $model)
    {
        return $model->newQuery()
            ->with(['user', 'students', 'activeSubscriptions'])
            ->select('clients.*');
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('admin-clients-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->buttons([
                [
                    'text' => '<i class="fas fa-plus"></i> ' . __('messages.add_client'),
                    'className' => 'btn btn-primary',
                    'action' => 'function() { window.location.href = "' . route('admin.clients.create') . '"; }'
                ],
                [
                    'extend' => 'collection',
                    'text' => '<i class="fas fa-download"></i> Export',
                    'className' => 'btn btn-secondary',
                    'buttons' => [
                        [
                            'extend' => 'csv',
                            'text' => '<i class="fas fa-file-csv"></i> CSV'
                        ],
                        [
                            'extend' => 'excel',
                            'text' => '<i class="fas fa-file-excel"></i> Excel'
                        ],
                        [
                            'extend' => 'pdf',
                            'text' => '<i class="fas fa-file-pdf"></i> PDF'
                        ]
                    ]
                ],
                [
                    'extend' => 'print',
                    'text' => '<i class="fas fa-print"></i> Print',
                    'className' => 'btn btn-secondary'
                ],
                [
                    'text' => '<i class="fas fa-sync"></i> Reload',
                    'className' => 'btn btn-secondary',
                    'action' => 'function() { window.location.reload(); }'
                ]
            ])
            ->language($this->getLang());
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->title('ID')->width(60),
            Column::make('name')->title(__('messages.name'))->searchable(true),
            Column::make('email')->title(__('messages.email'))->searchable(true),
            Column::make('phone')->title(__('messages.phone'))->searchable(true),
            Column::make('area')->title(__('messages.area'))->searchable(true),
            Column::make('students_count')->title(__('messages.students'))->orderable(false)->searchable(false),
            Column::make('active_subscriptions')->title(__('messages.active_subscriptions'))->orderable(false)->searchable(false),
            Column::make('created_at')->title(__('messages.created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(120)
                ->addClass('text-center')
                ->title(__('messages.actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'AdminClients_' . date('YmdHis');
    }

    /**
     * Get language file for DataTable
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }
}
