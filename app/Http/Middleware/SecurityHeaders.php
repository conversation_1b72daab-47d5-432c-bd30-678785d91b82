<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only apply security headers in production
        if (app()->environment('production')) {
            $headers = config('security.headers');
            $csp = config('security.csp');

            // Set security headers
            $response->headers->set('X-Content-Type-Options', $headers['x_content_type_options']);
            $response->headers->set('X-Frame-Options', $headers['x_frame_options']);
            $response->headers->set('X-XSS-Protection', $headers['x_xss_protection']);
            $response->headers->set('Referrer-Policy', $headers['referrer_policy']);
            $response->headers->set('Permissions-Policy', $headers['permissions_policy']);

            // Set HSTS header for HTTPS
            if ($request->secure()) {
                $hstsValue = 'max-age=' . $headers['hsts_max_age'];
                if ($headers['hsts_include_subdomains']) {
                    $hstsValue .= '; includeSubDomains';
                }
                if ($headers['hsts_preload']) {
                    $hstsValue .= '; preload';
                }
                $response->headers->set('Strict-Transport-Security', $hstsValue);
            }

            // Build and set Content Security Policy
            $cspDirectives = [];
            foreach ($csp as $directive => $value) {
                $cspDirectives[] = str_replace('_', '-', $directive) . ' ' . $value;
            }
            $response->headers->set('Content-Security-Policy', implode('; ', $cspDirectives));
        }

        return $response;
    }
}
