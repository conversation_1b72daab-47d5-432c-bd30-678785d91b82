<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class LocaleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Available locales
        $availableLocales = ['en', 'ar'];

        // Get locale from various sources in order of priority
        $locale = $this->getLocaleFromSources($request, $availableLocales);

        // Set the application locale
        App::setLocale($locale);

        // Store in session for future requests
        Session::put('locale', $locale);

        // Update user's preferred language if authenticated
        if (Auth::check() && Auth::user()->lang !== $locale) {
            Auth::user()->update(['lang' => $locale]);
        }

        return $next($request);
    }

    /**
     * Get locale from various sources in order of priority
     */
    private function getLocaleFromSources(Request $request, array $availableLocales): string
    {
        // 1. Check URL parameter (highest priority for explicit language switching)
        if ($request->has('lang') && in_array($request->get('lang'), $availableLocales)) {
            return $request->get('lang');
        }

        // 2. Check session (recent language switch should take precedence)
        if (Session::has('locale') && in_array(Session::get('locale'), $availableLocales)) {
            return Session::get('locale');
        }

        // 3. Check authenticated user's preference (fallback to user's saved preference)
        if (Auth::check() && Auth::user()->lang && in_array(Auth::user()->lang, $availableLocales)) {
            return Auth::user()->lang;
        }

        // 4. Check browser's Accept-Language header
        $browserLocale = $this->getBrowserLocale($request, $availableLocales);
        if ($browserLocale) {
            return $browserLocale;
        }

        // 5. Fall back to application default
        return config('app.locale', 'ar');
    }

    /**
     * Get locale from browser's Accept-Language header
     */
    private function getBrowserLocale(Request $request, array $availableLocales): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');

        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        preg_match_all('/([a-z]{1,8}(?:-[a-z]{1,8})?)\s*(?:;\s*q\s*=\s*(1|0\.[0-9]+))?/i', $acceptLanguage, $matches);

        if (count($matches[1])) {
            $languages = array_combine($matches[1], $matches[2]);

            foreach ($languages as $lang => $quality) {
                $lang = strtolower(substr($lang, 0, 2)); // Get language code only

                if (in_array($lang, $availableLocales)) {
                    return $lang;
                }
            }
        }

        return null;
    }
}
