<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;

class EnhancedRateLimiting
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $type = 'default', int $maxAttempts = 60, int $decayMinutes = 1)
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type, $maxAttempts);
        $decayMinutes = $this->getDecayMinutes($type, $decayMinutes);

        // Check if rate limit is exceeded
        if ($this->tooManyAttempts($key, $maxAttempts)) {
            $this->logRateLimitExceeded($request, $type, $key);
            return $this->buildResponse($key, $maxAttempts, $decayMinutes);
        }

        // Increment attempt counter
        $this->hit($key, $decayMinutes);

        $response = $next($request);

        // Add rate limit headers
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts),
            $this->availableAt($key)
        );
    }

    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request, string $type): string
    {
        $user = $request->user();
        
        // For authenticated users, use user ID + type
        if ($user) {
            return "rate_limit:{$type}:user:{$user->id}";
        }

        // For guests, use IP + type
        return "rate_limit:{$type}:ip:" . $request->getClientIp();
    }

    /**
     * Get max attempts based on type and configuration
     */
    protected function getMaxAttempts(string $type, int $default): int
    {
        $limits = config('security.rate_limiting', []);
        
        return $limits[$type]['requests'] ?? $default;
    }

    /**
     * Get decay minutes based on type and configuration
     */
    protected function getDecayMinutes(string $type, int $default): int
    {
        $limits = config('security.rate_limiting', []);
        
        return $limits[$type]['decay_minutes'] ?? $default;
    }

    /**
     * Determine if the given key has been "accessed" too many times
     */
    protected function tooManyAttempts(string $key, int $maxAttempts): bool
    {
        return $this->attempts($key) >= $maxAttempts;
    }

    /**
     * Get the number of attempts for the given key
     */
    protected function attempts(string $key): int
    {
        return Cache::get($key, 0);
    }

    /**
     * Increment the counter for a given key for a given decay time
     */
    protected function hit(string $key, int $decayMinutes): int
    {
        $attempts = Cache::get($key, 0) + 1;
        Cache::put($key, $attempts, now()->addMinutes($decayMinutes));
        
        return $attempts;
    }

    /**
     * Calculate remaining attempts
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return max(0, $maxAttempts - $this->attempts($key));
    }

    /**
     * Get the time when the key will be available again
     */
    protected function availableAt(string $key): int
    {
        $cacheKey = $key . ':timer';
        $availableAt = Cache::get($cacheKey);
        
        if (!$availableAt) {
            $availableAt = now()->addMinutes($this->getDecayMinutes('default', 1))->timestamp;
            Cache::put($cacheKey, $availableAt, now()->addMinutes($this->getDecayMinutes('default', 1)));
        }
        
        return $availableAt;
    }

    /**
     * Create a 'too many attempts' response
     */
    protected function buildResponse(string $key, int $maxAttempts, int $decayMinutes): Response
    {
        $retryAfter = $this->getTimeUntilNextRetry($key);
        
        $response = response()->json([
            'message' => 'Too many attempts. Please try again later.',
            'retry_after' => $retryAfter,
            'max_attempts' => $maxAttempts,
        ], 429);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            0,
            $this->availableAt($key),
            $retryAfter
        );
    }

    /**
     * Add rate limit headers to response
     */
    protected function addHeaders($response, int $maxAttempts, int $remainingAttempts, int $availableAt, int $retryAfter = null)
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
            'X-RateLimit-Reset' => $availableAt,
        ]);

        if ($retryAfter !== null) {
            $response->headers->add(['Retry-After' => $retryAfter]);
        }

        return $response;
    }

    /**
     * Get the number of seconds until the next retry
     */
    protected function getTimeUntilNextRetry(string $key): int
    {
        return max(1, $this->availableAt($key) - time());
    }

    /**
     * Log rate limit exceeded event
     */
    protected function logRateLimitExceeded(Request $request, string $type, string $key)
    {
        if (config('security.audit_logging.log_rate_limit_exceeded', true)) {
            Log::channel('security')->warning('Rate limit exceeded', [
                'type' => $type,
                'key' => $key,
                'ip' => $request->getClientIp(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'timestamp' => now()->toISOString(),
            ]);
        }

        // Check for potential abuse patterns
        $this->checkForAbuse($request, $type);
    }

    /**
     * Check for potential abuse patterns
     */
    protected function checkForAbuse(Request $request, string $type)
    {
        $ip = $request->getClientIp();
        $abuseKey = "abuse_detection:ip:{$ip}";
        
        // Count rate limit violations in the last hour
        $violations = Cache::get($abuseKey, 0) + 1;
        Cache::put($abuseKey, $violations, now()->addHour());

        // If too many violations, consider it potential abuse
        if ($violations >= 10) {
            Log::channel('security')->alert('Potential abuse detected', [
                'ip' => $ip,
                'violations' => $violations,
                'type' => $type,
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString(),
            ]);

            // Optionally block the IP temporarily
            if (config('security.ip_blocking.enabled', false)) {
                $this->temporarilyBlockIp($ip);
            }
        }
    }

    /**
     * Temporarily block an IP address
     */
    protected function temporarilyBlockIp(string $ip)
    {
        $blockKey = "blocked_ip:{$ip}";
        $blockDuration = config('security.ip_blocking.block_duration_minutes', 60);
        
        Cache::put($blockKey, true, now()->addMinutes($blockDuration));
        
        Log::channel('security')->warning('IP temporarily blocked', [
            'ip' => $ip,
            'duration_minutes' => $blockDuration,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Check if an IP is currently blocked
     */
    public static function isIpBlocked(string $ip): bool
    {
        return Cache::has("blocked_ip:{$ip}");
    }
}
