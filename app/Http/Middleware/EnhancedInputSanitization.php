<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EnhancedInputSanitization
{
    /**
     * Suspicious patterns that might indicate malicious input
     */
    private $suspiciousPatterns = [
        // SQL Injection patterns
        '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
        '/(\bselect\b.*\bfrom\b.*\bwhere\b)/i',
        '/(\bdrop\b.*\btable\b)|(\bdelete\b.*\bfrom\b)/i',
        '/(\binsert\b.*\binto\b)|(\bupdate\b.*\bset\b)/i',

        // XSS patterns
        '/<script[^>]*>.*?<\/script>/is',
        '/javascript:/i',
        '/on\w+\s*=/i',
        '/<iframe[^>]*>.*?<\/iframe>/is',

        // Command injection patterns
        '/[\|&;]|\$\(|`/i',
        '/\b(eval|exec|system|shell_exec|passthru)\b/i',

        // Path traversal patterns
        '/\.\.[\/\\\\]/i',
        '/\/(etc|proc|sys|dev)\//i',

        // LDAP injection patterns
        '/[\(\)\*\|&]/i',
    ];

    /**
     * Fields that should not be sanitized (like passwords)
     */
    private $excludedFields = [
        'password',
        'password_confirmation',
        'current_password',
        'new_password',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        if (config('security.sanitization.enabled', true)) {
            $this->sanitizeInput($request);
        }

        return $next($request);
    }

    /**
     * Sanitize request input
     */
    private function sanitizeInput(Request $request)
    {
        $input = $request->all();
        $sanitized = $this->sanitizeArray($input, $request);
        
        // Replace request input with sanitized data
        $request->replace($sanitized);
    }

    /**
     * Recursively sanitize array data
     */
    private function sanitizeArray(array $data, Request $request, string $parentKey = ''): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            $fullKey = $parentKey ? "{$parentKey}.{$key}" : $key;

            // Skip excluded fields
            if (in_array($key, $this->excludedFields) || in_array($fullKey, $this->excludedFields)) {
                $sanitized[$key] = $value;
                continue;
            }

            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value, $request, $fullKey);
            } elseif (is_string($value)) {
                $sanitized[$key] = $this->sanitizeString($value, $fullKey, $request);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize string input
     */
    private function sanitizeString(string $value, string $fieldName, Request $request): string
    {
        $originalValue = $value;

        // Check for suspicious patterns
        if (config('security.sanitization.block_suspicious_patterns', true)) {
            foreach ($this->suspiciousPatterns as $pattern) {
                try {
                    if (preg_match($pattern, $value)) {
                        $this->logSuspiciousActivity($fieldName, $value, $pattern, $request);

                        // Block the request if it contains highly suspicious content
                        if ($this->isHighRisk($pattern)) {
                            abort(400, 'Malicious input detected');
                        }
                    }
                } catch (\Exception $e) {
                    // Log regex compilation errors but don't break the application
                    \Log::channel('security')->error('Regex pattern error', [
                        'pattern' => $pattern,
                        'error' => $e->getMessage(),
                        'field' => $fieldName
                    ]);
                }
            }
        }

        // Remove null bytes
        if (config('security.sanitization.remove_null_bytes', true)) {
            $value = str_replace("\0", '', $value);
        }

        // Trim whitespace
        if (config('security.sanitization.trim_whitespace', true)) {
            $value = trim($value);
        }

        // Strip dangerous tags but preserve safe HTML for content fields
        if (config('security.sanitization.strip_tags', true)) {
            if ($this->isContentField($fieldName)) {
                // Allow safe HTML tags for content fields
                $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6><blockquote>';
                $value = strip_tags($value, $allowedTags);
            } else {
                // Strip all HTML tags for other fields
                $value = strip_tags($value);
            }
        }

        // Encode special characters for display fields
        if ($this->isDisplayField($fieldName)) {
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }

        // Log if value was modified
        if ($originalValue !== $value && config('security.audit_logging.log_sanitization', false)) {
            Log::channel('security')->info('Input sanitized', [
                'field' => $fieldName,
                'original_length' => strlen($originalValue),
                'sanitized_length' => strlen($value),
                'ip' => $request->getClientIp(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
            ]);
        }

        return $value;
    }

    /**
     * Check if a pattern is high risk
     */
    private function isHighRisk(string $pattern): bool
    {
        $highRiskPatterns = [
            '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
            '/(\bdrop\b.*\btable\b)|(\bdelete\b.*\bfrom\b)/i',
            '/<script[^>]*>.*?<\/script>/is',
            '/\b(eval|exec|system|shell_exec|passthru)\b/i',
        ];

        return in_array($pattern, $highRiskPatterns);
    }

    /**
     * Check if field is a content field that may contain HTML
     */
    private function isContentField(string $fieldName): bool
    {
        $contentFields = [
            'description',
            'content',
            'body',
            'message',
            'notes',
            'comments',
            'details',
        ];

        foreach ($contentFields as $contentField) {
            if (str_contains(strtolower($fieldName), $contentField)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if field is a display field that should be HTML encoded
     */
    private function isDisplayField(string $fieldName): bool
    {
        $displayFields = [
            'name',
            'title',
            'label',
            'caption',
            'alt',
        ];

        foreach ($displayFields as $displayField) {
            if (str_contains(strtolower($fieldName), $displayField)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log suspicious activity
     */
    private function logSuspiciousActivity(string $fieldName, string $value, string $pattern, Request $request)
    {
        if (config('security.audit_logging.log_suspicious_patterns', true)) {
            Log::channel('security')->warning('Suspicious input detected', [
                'field' => $fieldName,
                'pattern' => $pattern,
                'value_length' => strlen($value),
                'value_preview' => substr($value, 0, 100),
                'ip' => $request->getClientIp(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'timestamp' => now()->toISOString(),
            ]);
        }
    }
}
