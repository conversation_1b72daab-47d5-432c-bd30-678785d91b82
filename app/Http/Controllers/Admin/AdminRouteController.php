<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TransportRoute;
use App\Models\Driver;
use App\DataTables\AdminRoutesDataTable;
use App\Http\Requests\Admin\StoreTransportRouteRequest;
use App\Http\Requests\Admin\UpdateTransportRouteRequest;
use App\Traits\AdminAuditLogging;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminRouteController extends Controller
{
    use AdminAuditLogging;

    public function __construct()
    {
        $this->middleware(['permission:read admin_routes'])->only(['index', 'show']);
        $this->middleware(['permission:create admin_routes'])->only(['create', 'store']);
        $this->middleware(['permission:update admin_routes'])->only(['edit', 'update']);
        $this->middleware(['permission:delete admin_routes'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AdminRoutesDataTable $dataTable)
    {
        $this->logAdminAction('viewed', null, [], request());

        return $dataTable->render('admin.routes.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drivers = Driver::active()->get();
        
        return view('admin.routes.create', compact('drivers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTransportRouteRequest $request)
    {
        $route = null;

        DB::transaction(function () use ($request, &$route) {
            $route = TransportRoute::create([
                'name' => $request->name,
                'from_area' => $request->from_area,
                'to_school' => $request->to_school,
                'pickup_time' => $request->pickup_time,
                'dropoff_time' => $request->dropoff_time,
                'monthly_price' => $request->monthly_price,
                'capacity' => $request->capacity,
                'current_students' => 0,
                'status' => $request->status,
                'description' => $request->description,
                'stops' => $request->stops ? array_filter($request->stops) : null,
            ]);

            // Assign driver if selected
            if ($request->driver_id) {
                $route->drivers()->attach($request->driver_id, [
                    'assigned_date' => now(),
                    'status' => 'active',
                ]);
            }
        });

        // Log the creation
        $this->logAdminAction('created', $route, [], $request);

        return redirect()->route('admin.routes.index')
            ->with('success', __('messages.route_created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(TransportRoute $route)
    {
        $route->load(['subscriptions.client.user', 'activeDrivers', 'drivers']);

        // Log the view action
        $this->logAdminAction('viewed', $route, [], request());

        return view('admin.routes.show', compact('route'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TransportRoute $route)
    {
        $drivers = Driver::active()->get();
        $route->load('activeDrivers');
        
        return view('admin.routes.edit', compact('route', 'drivers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTransportRouteRequest $request, TransportRoute $route)
    {
        // Store original data for change tracking
        $originalData = $route->toArray();

        DB::transaction(function () use ($request, $route) {
            $route->update([
                'name' => $request->name,
                'from_area' => $request->from_area,
                'to_school' => $request->to_school,
                'pickup_time' => $request->pickup_time,
                'dropoff_time' => $request->dropoff_time,
                'monthly_price' => $request->monthly_price,
                'capacity' => $request->capacity,
                'status' => $request->status,
                'description' => $request->description,
                'stops' => $request->stops ? array_filter($request->stops) : null,
            ]);

            // Update driver assignment
            $currentDriver = $route->currentDriver();
            
            if ($request->driver_id) {
                if (!$currentDriver || $currentDriver->id != $request->driver_id) {
                    // End current driver assignment
                    if ($currentDriver) {
                        $route->drivers()->updateExistingPivot($currentDriver->id, [
                            'status' => 'inactive',
                            'end_date' => now(),
                        ]);
                    }
                    
                    // Assign new driver
                    $route->drivers()->attach($request->driver_id, [
                        'assigned_date' => now(),
                        'status' => 'active',
                    ]);
                }
            } else {
                // Remove current driver assignment
                if ($currentDriver) {
                    $route->drivers()->updateExistingPivot($currentDriver->id, [
                        'status' => 'inactive',
                        'end_date' => now(),
                    ]);
                }
            }
        });

        // Log the update with changes
        $changes = $this->getModelChanges($route, $originalData);
        $this->logAdminAction('updated', $route, $changes, $request);

        return redirect()->route('admin.routes.index')
            ->with('success', __('messages.route_updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TransportRoute $route)
    {
        if ($route->subscriptions()->exists()) {
            return redirect()->route('admin.routes.index')
                ->with('error', __('messages.cannot_delete_route_with_subscriptions'));
        }

        // Store route info before deletion for logging
        $routeInfo = $route->load(['subscriptions', 'activeDrivers']);

        DB::transaction(function () use ($route) {
            // End all driver assignments
            $route->drivers()->updateExistingPivot(
                $route->drivers()->pluck('drivers.id')->toArray(),
                ['status' => 'inactive', 'end_date' => now()]
            );

            $route->delete();
        });

        // Log the deletion
        $this->logAdminAction('deleted', $routeInfo, [], request());

        return redirect()->route('admin.routes.index')
            ->with('success', __('messages.route_deleted_successfully'));
    }

    /**
     * Assign driver to route
     */
    public function assignDriver(Request $request, TransportRoute $route)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
        ]);

        $currentDriver = $route->currentDriver();
        
        if ($currentDriver) {
            return redirect()->back()
                ->with('error', 'Route already has an assigned driver.');
        }

        $route->drivers()->attach($request->driver_id, [
            'assigned_date' => now(),
            'status' => 'active',
        ]);

        return redirect()->back()
            ->with('success', 'Driver assigned successfully.');
    }

    /**
     * Show capacity report for routes
     */
    public function showCapacityReport()
    {
        $routes = TransportRoute::with(['subscriptions'])
            ->where('status', 'active')
            ->get()
            ->map(function ($route) {
                return [
                    'id' => $route->id,
                    'name' => $route->name,
                    'capacity' => $route->capacity,
                    'current_students' => $route->subscriptions->where('status', 'active')->count(),
                    'utilization' => $route->capacity > 0 ? round(($route->subscriptions->where('status', 'active')->count() / $route->capacity) * 100, 2) : 0,
                    'available_seats' => $route->capacity - $route->subscriptions->where('status', 'active')->count(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $routes
        ]);
    }

    /**
     * Show driver assignments overview
     */
    public function showDriverAssignments()
    {
        $assignments = TransportRoute::with(['activeDrivers', 'drivers'])
            ->where('status', 'active')
            ->get()
            ->map(function ($route) {
                $currentDriver = $route->currentDriver();
                return [
                    'route_id' => $route->id,
                    'route_name' => $route->name,
                    'driver_name' => $currentDriver ? $currentDriver->name : null,
                    'driver_id' => $currentDriver ? $currentDriver->id : null,
                    'assigned_date' => $currentDriver ? $currentDriver->pivot->assigned_date : null,
                    'status' => $currentDriver ? 'assigned' : 'unassigned',
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Show schedule overview
     */
    public function showScheduleOverview()
    {
        $schedules = TransportRoute::where('status', 'active')
            ->orderBy('pickup_time')
            ->get()
            ->map(function ($route) {
                return [
                    'route_id' => $route->id,
                    'route_name' => $route->name,
                    'pickup_time' => $route->pickup_time,
                    'dropoff_time' => $route->dropoff_time,
                    'from_area' => $route->from_area,
                    'to_school' => $route->to_school,
                    'students_count' => $route->subscriptions->where('status', 'active')->count(),
                    'driver' => $route->currentDriver() ? $route->currentDriver()->name : 'No driver assigned',
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $schedules
        ]);
    }

    /**
     * Remove driver from route
     */
    public function removeDriver(TransportRoute $route, Driver $driver)
    {
        $route->drivers()->updateExistingPivot($driver->id, [
            'status' => 'inactive',
            'end_date' => now(),
        ]);

        return redirect()->back()
            ->with('success', 'Driver removed from route.');
    }
}
