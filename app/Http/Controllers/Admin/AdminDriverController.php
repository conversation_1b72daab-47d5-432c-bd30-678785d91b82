<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\TransportRoute;
use App\DataTables\AdminDriversDataTable;
use App\Http\Requests\Admin\StoreSimpleDriverRequest;
use App\Http\Requests\Admin\UpdateDriverRequest;
use App\Traits\AdminAuditLogging;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDriverController extends Controller
{
    use AdminAuditLogging;

    public function __construct()
    {
        $this->middleware(['permission:read admin_drivers'])->only(['index', 'show']);
        $this->middleware(['permission:create admin_drivers'])->only(['create', 'store']);
        $this->middleware(['permission:update admin_drivers'])->only(['edit', 'update']);
        $this->middleware(['permission:delete admin_drivers'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AdminDriversDataTable $dataTable)
    {
        $this->logAdminAction('viewed', null, [], request());

        return $dataTable->render('admin.drivers.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.drivers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSimpleDriverRequest $request)
    {
        $driver = Driver::create($request->validated());

        // Log the creation
        $this->logAdminAction('created', $driver, [], $request);

        return redirect()->route('admin.drivers.index')
            ->with('success', __('messages.driver_created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Driver $driver)
    {
        $driver->load(['routes', 'activeRoutes']);

        // Log the view action
        $this->logAdminAction('viewed', $driver, [], request());

        return view('admin.drivers.show', compact('driver'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Driver $driver)
    {
        return view('admin.drivers.edit', compact('driver'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDriverRequest $request, Driver $driver)
    {
        // Store original data for change tracking
        $originalData = $driver->toArray();

        $driver->update($request->validated());

        // Log the update with changes
        $changes = $this->getModelChanges($driver, $originalData);
        $this->logAdminAction('updated', $driver, $changes, $request);

        return redirect()->route('admin.drivers.index')
            ->with('success', __('messages.driver_updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Driver $driver)
    {
        if ($driver->hasActiveRoute()) {
            return redirect()->route('admin.drivers.index')
                ->with('error', __('messages.cannot_delete_driver_with_active_routes'));
        }

        // Store driver info before deletion for logging
        $driverInfo = $driver->load(['routes']);

        DB::transaction(function () use ($driver) {
            // End all route assignments
            $driver->routes()->updateExistingPivot(
                $driver->routes()->pluck('routes.id')->toArray(),
                ['status' => 'inactive', 'end_date' => now()]
            );

            $driver->delete();
        });

        // Log the deletion
        $this->logAdminAction('deleted', $driverInfo, [], request());

        return redirect()->route('admin.drivers.index')
            ->with('success', __('messages.driver_deleted_successfully'));
    }

    /**
     * Assign driver to route
     */
    public function assignRoute(Request $request, Driver $driver)
    {
        $request->validate([
            'route_id' => 'required|exists:routes,id',
        ]);

        if ($driver->hasActiveRoute()) {
            return redirect()->back()
                ->with('error', 'Driver already has an active route assignment.');
        }

        $route = TransportRoute::find($request->route_id);
        
        if ($route->currentDriver()) {
            return redirect()->back()
                ->with('error', 'Route already has an assigned driver.');
        }

        $driver->routes()->attach($request->route_id, [
            'assigned_date' => now(),
            'status' => 'active',
        ]);

        return redirect()->back()
            ->with('success', 'Driver assigned to route successfully.');
    }

    /**
     * Remove driver from route
     */
    public function removeRoute(Driver $driver, TransportRoute $route)
    {
        $driver->routes()->updateExistingPivot($route->id, [
            'status' => 'inactive',
            'end_date' => now(),
        ]);

        return redirect()->back()
            ->with('success', __('messages.driver_removed_from_route'));
    }

    /**
     * Get drivers with expiring licenses
     */
    public function expiringLicenses()
    {
        $drivers = Driver::withExpiringLicense(30)
            ->active()
            ->orderBy('license_expiry')
            ->get();

        return view('admin.drivers.expiring-licenses', compact('drivers'));
    }

    /**
     * Suspend driver
     */
    public function suspend(Driver $driver)
    {
        $driver->update(['status' => 'suspended']);

        // End active route assignments
        $driver->routes()->updateExistingPivot(
            $driver->activeRoutes()->pluck('routes.id')->toArray(),
            ['status' => 'inactive', 'end_date' => now()]
        );

        return redirect()->back()
            ->with('success', __('messages.driver_suspended_successfully'));
    }

    /**
     * Activate driver
     */
    public function activate(Driver $driver)
    {
        $driver->update(['status' => 'active']);

        return redirect()->back()
            ->with('success', __('messages.driver_activated_successfully'));
    }

    /**
     * Show unassigned drivers
     */
    public function showUnassignedDrivers()
    {
        $unassignedDrivers = Driver::whereDoesntHave('routes', function ($query) {
            $query->where('status', 'active');
        })
        ->where('status', 'active')
        ->get()
        ->map(function ($driver) {
            return [
                'id' => $driver->id,
                'name' => $driver->name,
                'phone' => $driver->phone,
                'license_number' => $driver->license_number,
                'license_expiry' => $driver->license_expiry,
                'hire_date' => $driver->hire_date,
                'vehicle_info' => $driver->vehicle_type . ' - ' . $driver->vehicle_plate,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $unassignedDrivers
        ]);
    }

    /**
     * Show driver performance metrics
     */
    public function showDriverPerformance()
    {
        $drivers = Driver::with(['routes', 'activeRoutes'])
            ->where('status', 'active')
            ->get()
            ->map(function ($driver) {
                $currentRoute = $driver->currentRoute();
                $totalRoutes = $driver->routes->count();
                $activeRoutes = $driver->activeRoutes->count();

                return [
                    'id' => $driver->id,
                    'name' => $driver->name,
                    'current_route' => $currentRoute ? $currentRoute->name : 'No active route',
                    'total_routes_assigned' => $totalRoutes,
                    'active_routes' => $activeRoutes,
                    'experience_months' => $driver->hire_date ? now()->diffInMonths($driver->hire_date) : 0,
                    'license_status' => $driver->license_expiry > now() ? 'Valid' : 'Expired',
                    'performance_score' => $this->calculatePerformanceScore($driver),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $drivers
        ]);
    }

    /**
     * Calculate driver performance score
     */
    private function calculatePerformanceScore($driver)
    {
        $score = 100;

        // Deduct points for expired license
        if ($driver->license_expiry <= now()) {
            $score -= 30;
        }

        // Deduct points for license expiring soon (within 30 days)
        if ($driver->license_expiry <= now()->addDays(30)) {
            $score -= 10;
        }

        // Add points for experience
        $experienceMonths = $driver->hire_date ? now()->diffInMonths($driver->hire_date) : 0;
        $score += min($experienceMonths * 0.5, 20); // Max 20 points for experience

        // Ensure score is between 0 and 100
        return max(0, min(100, round($score)));
    }
}
