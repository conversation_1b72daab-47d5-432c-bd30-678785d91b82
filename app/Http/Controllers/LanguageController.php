<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class LanguageController extends Controller
{
    /**
     * Available languages
     */
    private $availableLanguages = [
        'en' => 'English',
        'ar' => 'العربية'
    ];

    /**
     * Switch application language
     */
    public function switch(Request $request, $locale)
    {
        // Validate locale
        if (!array_key_exists($locale, $this->availableLanguages)) {
            abort(400, 'Invalid language');
        }

        // Set application locale
        App::setLocale($locale);

        // Store in session
        Session::put('locale', $locale);

        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['lang' => $locale]);
        }

        // Redirect back to previous page or home
        return Redirect::back()->with('success', __('messages.language_changed_successfully'));
    }

    /**
     * Get available languages
     */
    public function getAvailableLanguages()
    {
        return response()->json([
            'current' => App::getLocale(),
            'available' => $this->availableLanguages
        ]);
    }

    /**
     * Get current language info
     */
    public function getCurrentLanguage()
    {
        $currentLocale = App::getLocale();
        
        return response()->json([
            'code' => $currentLocale,
            'name' => $this->availableLanguages[$currentLocale] ?? 'Unknown',
            'direction' => $currentLocale === 'ar' ? 'rtl' : 'ltr'
        ]);
    }
}
