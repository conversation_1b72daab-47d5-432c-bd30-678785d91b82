<?php

namespace Tests\Feature\Admin;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Client;
use App\Models\UserTypes;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

class ClientManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminUser;
    protected $clientUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user types
        UserTypes::create(['id' => 1, 'name' => 'Admin']);
        UserTypes::create(['id' => 2, 'name' => 'Client']);

        // Create permissions
        Permission::create(['name' => 'view admin_clients', 'guard_name' => 'web']);
        Permission::create(['name' => 'create admin_clients', 'guard_name' => 'web']);
        Permission::create(['name' => 'update admin_clients', 'guard_name' => 'web']);
        Permission::create(['name' => 'delete admin_clients', 'guard_name' => 'web']);

        // Create admin role
        $adminRole = Role::create(['name' => 'admin', 'guard_name' => 'web']);
        $adminRole->givePermissionTo(['view admin_clients', 'create admin_clients', 'update admin_clients', 'delete admin_clients']);

        // Create admin user
        $this->adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '01000000000',
            'password' => Hash::make('password'),
            'user_type_id' => 1,
            'email_verified_at' => now(),
        ]);
        $this->adminUser->assignRole('admin');

        // Create client user
        $this->clientUser = User::create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'phone' => '01111111111',
            'password' => Hash::make('password'),
            'user_type_id' => 2,
            'email_verified_at' => now(),
        ]);
    }

    public function test_admin_can_view_clients_index()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.clients.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.clients.index');
    }

    public function test_admin_can_view_client_create_form()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.clients.create'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.clients.create');
    }
}
