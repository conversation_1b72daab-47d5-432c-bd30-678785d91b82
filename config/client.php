<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default User Password
    |--------------------------------------------------------------------------
    |
    | This password will be used when creating user accounts during client
    | registration. Users can change this password after their first login.
    |
    */
    'default_user_password' => env('CLIENT_DEFAULT_PASSWORD', 'RoyalTransit2024!'),

    'registration' => [
        'enabled' => env('CLIENT_REGISTRATION_ENABLED', true),
        'require_approval' => env('CLIENT_REQUIRE_APPROVAL', false),
        'auto_create_user_account' => true,
        'send_welcome_email' => env('CLIENT_SEND_WELCOME_EMAIL', true),
    ],

    'validation' => [
        'phone_regex' => '/^01[0-9]{9}$/', // Egyptian phone number format
        'required_fields' => [
            'father_phone',
            'address',
            'area',
            'students' => [
                'name',
                'school',
                'education_stage',
                'class_level',
            ]
        ],
    ],

    'subscription' => [
        'types' => [
            'monthly' => 'Monthly',
            'semester' => 'Semester',
            'yearly' => 'Yearly',
        ],
        'default_type' => 'monthly',
        'auto_renew' => env('CLIENT_AUTO_RENEW_SUBSCRIPTION', false),
    ],

    'notifications' => [
        'payment_reminders' => env('CLIENT_PAYMENT_REMINDERS', true),
        'route_updates' => env('CLIENT_ROUTE_UPDATES', true),
        'general_announcements' => env('CLIENT_GENERAL_ANNOUNCEMENTS', true),
    ],

    'limits' => [
        'max_students_per_client' => env('CLIENT_MAX_STUDENTS', 5),
        'max_subscriptions_per_client' => env('CLIENT_MAX_SUBSCRIPTIONS', 3),
    ],
];
