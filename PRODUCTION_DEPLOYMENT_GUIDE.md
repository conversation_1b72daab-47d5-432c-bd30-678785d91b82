# Royal Transit - Production Deployment Guide

## 🚀 Production Readiness Overview

Your Laravel application has been enhanced with comprehensive production-ready features including:

- ✅ **Complete CRUD Operations** for all entities (clients, drivers, routes, subscriptions, payments)
- ✅ **Enhanced Translation System** with Arabic/English support
- ✅ **Advanced User Management** with role-based permissions using Spatie
- ✅ **Production Security Features** including rate limiting, input sanitization, and audit logging
- ✅ **Optimized Database** with proper indexes and foreign key constraints
- ✅ **Responsive Admin UI** with mobile navigation and consistent design patterns

## 📋 Pre-Deployment Checklist

### 1. Environment Configuration

```bash
# Update .env file for production
APP_ENV=production
APP_DEBUG=false
APP_URL=https://royaltransit.com.eg

# Security Settings
SESSION_SECURE_COOKIES=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Cache Configuration
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=error
LOG_STACK=single,daily
LOG_DAILY_DAYS=14
```

### 2. Database Setup

```bash
# Run migrations
php artisan migrate --force

# Seed production data
php artisan db:seed --class=ProductionSeeder

# Create enhanced permissions
php artisan db:seed --class=EnhancedPermissionSeeder
```

### 3. Performance Optimization

```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev

# Clear and cache everything
php artisan optimize
```

### 4. Security Configuration

```bash
# Generate application key if not set
php artisan key:generate

# Create storage link
php artisan storage:link

# Set proper file permissions
chmod -R 755 storage bootstrap/cache
chmod -R 644 storage/logs
```

## 🔐 Security Features Implemented

### 1. Enhanced Input Sanitization
- SQL injection protection
- XSS prevention
- Command injection blocking
- Path traversal protection
- LDAP injection prevention

### 2. Rate Limiting
- API rate limiting: 60 requests/minute
- Admin rate limiting: 120 requests/minute
- Login rate limiting: 5 attempts/minute
- Automatic IP blocking for abuse

### 3. Security Headers
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Content Security Policy
- HSTS for HTTPS

### 4. Audit Logging
- All admin actions logged
- Security events tracked
- IP address and user agent logging
- Risk level assessment

## 👥 User Management & Permissions

### Default Roles Created:
1. **Super Admin** - Full system access
2. **Admin** - Comprehensive access (excluding critical operations)
3. **Transport Manager** - Transport operations focused
4. **Financial Manager** - Financial operations focused
5. **Client Service** - Client-focused operations
6. **Operator** - Read-only with basic operations

### Default Admin Account:
- **Email**: <EMAIL>
- **Password**: RoyalTransit2024!
- **Role**: Super Admin

⚠️ **Important**: Change the default password immediately after first login!

## 🌐 Translation Support

### Supported Languages:
- **Arabic (ar)** - Primary language
- **English (en)** - Secondary language

### Language Switching:
- URL: `/language/{locale}` (en/ar)
- Automatic user preference saving
- RTL support for Arabic

## 📊 Database Optimizations

### Indexes Added:
- User lookup indexes (email, phone, type)
- Client area and type indexes
- Student school and education stage indexes
- Driver status and license expiry indexes
- Route capacity and pricing indexes
- Subscription status and date indexes
- Payment status and date indexes
- Audit log performance indexes

## 🔧 Admin Panel Features

### Enhanced UI/UX:
- Responsive design with mobile navigation
- Consistent widget design patterns
- Full-width charts in dashboard
- Improved navigation structure
- Better form validation and error handling

### CRUD Operations:
- **Clients**: Complete management with student records
- **Drivers**: Vehicle management and status tracking
- **Routes**: Capacity management and pricing
- **Subscriptions**: Status management and renewals
- **Payments**: Processing and invoice generation
- **Users**: Role assignment and bulk operations

## 🚨 Monitoring & Maintenance

### Log Files:
- **Application Logs**: `storage/logs/laravel.log`
- **Security Logs**: `storage/logs/security.log`
- **Audit Logs**: `storage/logs/audit.log`

### Regular Maintenance:
```bash
# Clear old logs (run weekly)
php artisan log:clear --days=30

# Optimize database (run monthly)
php artisan db:optimize

# Clear expired sessions
php artisan session:gc
```

## 🔍 Testing

### Run Production Readiness Check:
```bash
./scripts/production-readiness-check.sh
```

### Run Tests:
```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test tests/Feature/Admin/
```

## 📈 Performance Monitoring

### Key Metrics to Monitor:
- Response times for admin operations
- Database query performance
- Memory usage
- Cache hit rates
- Failed login attempts
- Rate limit violations

### Recommended Tools:
- Laravel Telescope (for development)
- New Relic or DataDog (for production)
- Laravel Horizon (for queue monitoring)

## 🔄 Backup Strategy

### Database Backups:
```bash
# Daily automated backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# Weekly full backup with compression
mysqldump -u username -p database_name | gzip > backup_$(date +%Y%m%d).sql.gz
```

### File Backups:
- Storage directory
- Environment files
- Custom configurations

## 🚀 Deployment Commands

### Quick Deployment Script:
```bash
#!/bin/bash
# Pull latest code
git pull origin main

# Install dependencies
composer install --optimize-autoloader --no-dev

# Run migrations
php artisan migrate --force

# Clear and cache
php artisan optimize

# Restart services
sudo systemctl restart php8.2-fpm
sudo systemctl restart nginx
```

## 📞 Support & Troubleshooting

### Common Issues:

1. **403 Errors**: Check file permissions and web server configuration
2. **Database Connection**: Verify credentials and server accessibility
3. **Cache Issues**: Clear all caches with `php artisan optimize:clear`
4. **Permission Errors**: Run `php artisan permission:cache-reset`

### Emergency Contacts:
- System Administrator: <EMAIL>
- Technical Support: <EMAIL>

---

## ✅ Final Verification

Before going live, ensure:
- [ ] All environment variables are set correctly
- [ ] Database migrations are complete
- [ ] Permissions are properly configured
- [ ] SSL certificate is installed and working
- [ ] Backup systems are in place
- [ ] Monitoring is configured
- [ ] All tests pass
- [ ] Production readiness check passes

**🎉 Your Royal Transit application is now production-ready!**
