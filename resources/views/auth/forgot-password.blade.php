<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('reset_password') }} - {{ __('royal_transit') }}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --main-blue: #1e3a8a;
            --main-gold: #fbbf24;
        }
        body {
            background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .btn-main {
            background: linear-gradient(45deg, var(--main-blue), var(--main-gold));
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-main:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: var(--main-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }
        .logo {
            max-width: 80px;
            height: auto;
        }
        .alert {
            border-radius: 10px;
        }
        .lang-switcher {
            position: fixed;
            top: 20px;
            {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="lang-switcher">
        @if (app()->getLocale() == 'ar')
            <a class="btn btn-outline-dark btn-sm" href="{{ route('language.switch', 'en') }}">
                <i class="fas fa-globe"></i> English
            </a>
        @else
            <a class="btn btn-outline-dark btn-sm" href="{{ route('language.switch', 'ar') }}">
                <i class="fas fa-globe"></i> العربية
            </a>
        @endif
    </div>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <img src="/images/logo.png" alt="Royal Transit Logo" class="logo mb-3">
                            <h2 class="mt-3 mb-1" style="color: #1e3a8a;">{{ __('reset_password') }}</h2>
                            <p class="text-muted">{{ __('enter_your_email') }}</p>
                        </div>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        @if (session('status'))
                            <div class="alert alert-success">
                                {{ session('status') }}
                            </div>
                        @endif
                        <form method="POST" action="{{ route('password.email') }}">
                            @csrf
                            <div class="mb-4">
                                <label for="email" class="form-label">{{ __('email_address') }}</label>
                                <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" 
                                       name="email" value="{{ old('email') }}" required autocomplete="email" autofocus>
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="d-grid gap-2 mb-4">
                                <button type="submit" class="btn btn-main text-white">
                                    <i class="fas fa-paper-plane me-2"></i>{{ __('send_password_reset_link') }}
                                </button>
                            </div>
                            <div class="text-center">
                                <a href="{{ route('login') }}" class="text-decoration-none" style="color: #1e3a8a;">
                                    <i class="fas fa-arrow-left me-2"></i>{{ __('back_to_login') }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ route('home') }}" class="text-decoration-none" style="color: #1e3a8a;">
                        <i class="fas fa-home me-2"></i>{{ __('back_to_home') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
