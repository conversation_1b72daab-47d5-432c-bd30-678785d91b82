<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - {{ __('messages.admin_panel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @if(app()->getLocale() == 'ar')
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    @endif

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    @if(app()->getLocale() == 'ar')
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    @else
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    @endif

    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css"
        href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">

    <!-- Custom Styles -->
    <style>
        :root {
            --main-blue: #1e3a8a;
            --main-gold: #f59e0b;
            --main-white: #ffffff;
            --sidebar-width: 280px;
        }

        body {
            font-family: {{ app()->getLocale() == 'ar' ? "'Cairo', sans-serif" : "'Figtree', sans-serif" }};
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Main Layout */
        .admin-wrapper {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar-custom {
            background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
            width: var(--sidebar-width);
            min-height: 100vh;
            position: fixed;
            top: 0;
            {{ app()->getLocale() == 'ar' ? 'right: 0' : 'left: 0' }};
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-custom::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-custom::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-custom::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar-custom::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        .sidebar-custom.collapsed {
            transform: translateX({{ app()->getLocale() == 'ar' ? '100%' : '-100%' }});
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 0.875rem 1.25rem;
            border-radius: 0.5rem;
            margin: 0.125rem 0.75rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX({{ app()->getLocale() == 'ar' ? '-5px' : '5px' }});
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            {{ app()->getLocale() == 'ar' ? 'margin-left: 0.75rem' : 'margin-right: 0.75rem' }};
        }

        /* Main Content */
        .main-content {
            {{ app()->getLocale() == 'ar' ? 'margin-right' : 'margin-left' }}: var(--sidebar-width);
            padding: 2rem;
            width: calc(100% - var(--sidebar-width));
            min-height: 100vh;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        /* Enhanced Page Header */
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--main-blue);
        }

        .page-header h1 {
            color: var(--main-blue);
            font-weight: 600;
            margin-bottom: 0;
        }

        .page-header .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 0;
        }

        .main-content.expanded {
            {{ app()->getLocale() == 'ar' ? 'margin-right' : 'margin-left' }}: 0;
            width: 100%;
        }

        /* Mobile Toggle Button */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            {{ app()->getLocale() == 'ar' ? 'right: 1rem' : 'left: 1rem' }};
            z-index: 1001;
            background: var(--main-blue);
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* Card Styles */
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
            color: white;
            border-radius: 0.75rem 0.75rem 0 0 !important;
            padding: 1rem 1.25rem;
            border-bottom: none;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* Stats Cards */
        .stats-card {
            border-radius: 1rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: none;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stats-card.stats-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stats-card.stats-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .stats-card.stats-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .stats-card.stats-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
            color: white;
        }

        .stats-card.stats-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .stats-card.stats-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .stats-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        /* Table Styles */
        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #f8f9fa;
            padding: 1rem 0.75rem;
        }

        .table td {
            padding: 0.875rem 0.75rem;
            vertical-align: middle;
        }

        /* Form Styles */
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #ced4da;
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--main-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }

        /* Button Styles */
        .btn {
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--main-gold) 0%, var(--main-blue) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Alert Styles */
        .alert {
            border-radius: 0.75rem;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        /* Badge Styles */
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 0.5rem;
        }

        /* RTL Specific Styles */
        [dir="rtl"] .sidebar .nav-link:hover,
        [dir="rtl"] .sidebar .nav-link.active {
            transform: translateX(-5px);
        }

        [dir="rtl"] .stats-icon {
            {{ app()->getLocale() == 'ar' ? 'margin-left: 1rem' : 'margin-right: 1rem' }};
        }

        [dir="rtl"] .btn i {
            {{ app()->getLocale() == 'ar' ? 'margin-left: 0.5rem; margin-right: 0' : 'margin-right: 0.5rem' }};
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .sidebar-toggle {
                display: block;
            }

            .sidebar-custom {
                transform: translateX({{ app()->getLocale() == 'ar' ? '100%' : '-100%' }});
            }

            .sidebar-custom.show {
                transform: translateX(0);
            }

            .main-content {
                {{ app()->getLocale() == 'ar' ? 'margin-right' : 'margin-left' }}: 0;
                width: 100%;
                padding-top: 5rem;
            }

            .stats-card h3 {
                font-size: 1.75rem;
            }

            .stats-icon {
                font-size: 2rem;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
                padding-top: 5rem;
            }

            .card-body {
                padding: 1rem;
            }

            .stats-card .card-body {
                padding: 1rem;
            }

            .stats-card h3 {
                font-size: 1.5rem;
            }

            .stats-icon {
                font-size: 1.75rem;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 0.75rem;
                padding-top: 5rem;
            }

            .stats-card h6 {
                font-size: 0.875rem;
            }

            .stats-card h3 {
                font-size: 1.25rem;
            }

            .stats-card small {
                font-size: 0.75rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 0.75rem;
            }
        }

        /* Loading Animation */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Smooth Transitions */
        * {
            transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
        }
    </style>

    @stack('styles')
</head>

<body>
    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Mobile Navigation -->
    @include('admin.partials.mobile-nav')

    <div class="admin-wrapper">
        @include('admin.partials.sidebar')

        <main class="main-content" id="mainContent">
            <!-- Page Header -->
            @if (isset($pageTitle))
                <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
                    <h1 class="h3 mb-0 mb-md-0">{{ $pageTitle }}</h1>
                    @if (isset($pageActions))
                        <div class="mt-2 mt-md-0">
                            {!! $pageActions !!}
                        </div>
                    @endif
                </div>
            @endif

            <!-- Flash Messages -->
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
                </div>
            @endif

            @if (session('warning'))
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ session('warning') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
                </div>
            @endif

            @if (session('info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ session('info') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
                </div>
            @endif

            <!-- Main Content -->
            @yield('content')
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <!-- Custom Scripts -->
    <script>
        // CSRF Token Setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Sidebar Toggle Functionality
        $(document).ready(function() {
            const sidebar = $('.sidebar-custom');
            const mainContent = $('.main-content');
            const sidebarToggle = $('#sidebarToggle');

            // Toggle sidebar on mobile
            sidebarToggle.on('click', function() {
                sidebar.toggleClass('show');
                if (sidebar.hasClass('show')) {
                    $(this).find('i').removeClass('fa-bars').addClass('fa-times');
                } else {
                    $(this).find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });

            // Close sidebar when clicking outside on mobile
            $(document).on('click', function(e) {
                if ($(window).width() <= 992) {
                    if (!$(e.target).closest('.sidebar-custom, .sidebar-toggle').length) {
                        sidebar.removeClass('show');
                        sidebarToggle.find('i').removeClass('fa-times').addClass('fa-bars');
                    }
                }
            });

            // Handle window resize
            $(window).on('resize', function() {
                if ($(window).width() > 992) {
                    sidebar.removeClass('show');
                    sidebarToggle.find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Confirm delete actions
        $(document).on('click', '.btn-delete', function(e) {
            if (!confirm('{{ __('messages.confirm_delete') }}')) {
                e.preventDefault();
                return false;
            }
        });

        // Loading state for forms
        $(document).on('submit', 'form', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            if (submitBtn.length) {
                submitBtn.prop('disabled', true);
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>' + '{{ __('messages.loading') }}');

                // Re-enable after 10 seconds as fallback
                setTimeout(function() {
                    submitBtn.prop('disabled', false).html(originalText);
                }, 10000);
            }
        });

        // Smooth scroll for anchor links
        $(document).on('click', 'a[href^="#"]', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });

        // Initialize tooltips
        $(function () {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });

        // Initialize popovers
        $(function () {
            $('[data-bs-toggle="popover"]').popover();
        });

        // Language switcher with loading state
        $(document).on('click', '.language-switcher', function(e) {
            e.preventDefault();
            const link = $(this);
            const originalText = link.html();

            link.html('<i class="fas fa-spinner fa-spin {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>' + '{{ __('messages.switching_language') }}');

            setTimeout(function() {
                window.location.href = link.attr('href');
            }, 500);
        });
    </script>

    @stack('scripts')
</body>

</html>
