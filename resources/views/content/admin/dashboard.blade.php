@extends('layouts.admin')

@php
    $pageTitle = __('messages.dashboard');
    $iconClass = app()->getLocale() == 'ar' ? 'ms-1' : 'me-1';
    $pageActions = '<div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportDashboard()">
                            <i class="fas fa-download ' . $iconClass . '"></i>' . __('messages.export') . '
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync ' . $iconClass . '"></i>' . __('messages.refresh') . '
                        </button>
                    </div>';
@endphp

@push('styles')
    <style>
        /* Dashboard Responsive Styles */
        @media (max-width: 768px) {
            .stats-card .card-body {
                padding: 1rem 0.75rem;
            }

            .stats-card h3 {
                font-size: 1.5rem;
            }

            .stats-icon {
                font-size: 1.5rem;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn-group .btn {
                margin-bottom: 0.25rem;
            }
        }

        @media (max-width: 576px) {
            .stats-card h6 {
                font-size: 0.875rem;
            }

            .stats-card h3 {
                font-size: 1.25rem;
            }

            .stats-card small {
                font-size: 0.75rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        /* Ensure cards have equal height */
        .stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        /* Better spacing for mobile */
        .g-3 {
            --bs-gutter-x: 1rem;
            --bs-gutter-y: 1rem;
        }

        @media (max-width: 576px) {
            .g-3 {
                --bs-gutter-x: 0.75rem;
                --bs-gutter-y: 0.75rem;
            }
        }

        /* System Health Styles */
        .health-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .health-healthy {
            background-color: #28a745;
        }

        .health-warning {
            background-color: #ffc107;
        }

        .health-error {
            background-color: #dc3545;
        }

        /* Alert Styles */
        .alert-dismissible .btn-close {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            padding: 1.25rem 1rem;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* Progress bars */
        .progress-sm {
            height: 0.5rem;
        }

        /* Activity timeline */
        .activity-item {
            border-left: 2px solid #e9ecef;
            padding-left: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .activity-item::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6c757d;
        }

        .activity-item.activity-success::before {
            background-color: #28a745;
        }

        .activity-item.activity-warning::before {
            background-color: #ffc107;
        }

        .activity-item.activity-danger::before {
            background-color: #dc3545;
        }
    </style>
@endpush

@section('content')
<!-- System Alerts -->
@if(count($alerts) > 0)
<div class="row mb-4">
    <div class="col-12">
        @foreach($alerts as $alert)
            <div class="alert alert-{{ $alert['type'] }} alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="{{ $alert['icon'] }} {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    <div class="flex-grow-1">
                        <strong>{{ $alert['title'] }}</strong>
                        <span class="{{ app()->getLocale() == 'ar' ? 'me-2' : 'ms-2' }}">{{ $alert['message'] }}</span>
                    </div>
                    @if(isset($alert['action_url']))
                        <a href="{{ $alert['action_url'] }}" class="btn btn-sm btn-outline-{{ $alert['type'] }} {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                            {{ $alert['action_text'] }}
                        </a>
                    @endif
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
            </div>
        @endforeach
    </div>
</div>
@endif

<!-- System Health Status -->
<div class="row g-3 mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center flex-wrap">
                <h6 class="m-0 fw-bold text-white mb-2 mb-md-0">{{ __('messages.system_health') }}</h6>
                <button class="btn btn-sm btn-outline-light" onclick="location.reload()">
                    <i class="fas fa-sync {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.refresh') }}
                </button>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['database_status']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.database_status') }}</small>
                            <small class="text-muted">{{ $systemHealth['database_status']['message'] }}</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['cache_status']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.cache_status') }}</small>
                            <small class="text-muted">{{ $systemHealth['cache_status']['message'] }}</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['storage_usage']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.storage_usage') }}</small>
                            <small class="text-muted">{{ $systemHealth['storage_usage']['usage_percentage'] }}%</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['memory_usage']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.memory_usage') }}</small>
                            <small class="text-muted">{{ $systemHealth['memory_usage']['usage_percentage'] }}%</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['queue_status']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.queue_status') }}</small>
                            <small class="text-muted">{{ $systemHealth['queue_status']['message'] }}</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 col-12">
                        <div class="text-center">
                            <span class="health-indicator health-{{ $systemHealth['uptime']['status'] }}"></span>
                            <small class="d-block">{{ __('messages.system_uptime') }}</small>
                            <small class="text-muted">{{ $systemHealth['uptime']['uptime_human'] }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card stats-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2 opacity-75">{{ __('messages.total_clients') }}</h6>
                        <h3 class="mb-0 fw-bold">{{ number_format($stats['total_clients']) }}</h3>
                        <small class="d-block opacity-75">
                            <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                            {{ $stats['new_clients_this_month'] }} {{ __('messages.this_month') }}
                        </small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                @can('view admin_clients')
                    <div class="mt-3">
                        <a href="{{ route('admin.clients.index') }}" class="btn btn-sm btn-light w-100">
                            <i class="fas fa-arrow-right {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.view_all') }}
                        </a>
                    </div>
                @endcan
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card stats-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2 opacity-75">{{ __('messages.active_routes') }}</h6>
                        <h3 class="mb-0 fw-bold">{{ number_format($stats['active_routes']) }}</h3>
                        <small class="d-block opacity-75">
                            {{ $stats['total_routes'] }} {{ __('messages.total') }}
                        </small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-route fa-2x"></i>
                    </div>
                </div>
                @can('view admin_routes')
                    <div class="mt-3">
                        <a href="{{ route('admin.routes.index') }}" class="btn btn-sm btn-light w-100">
                            <i class="fas fa-arrow-right {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.manage_routes') }}
                        </a>
                    </div>
                @endcan
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.active_drivers') }}</h6>
                        <h3 class="mb-0 fw-bold text-success">{{ number_format($stats['active_drivers']) }}</h3>
                        <small class="text-muted d-block">
                            {{ $stats['drivers_on_leave'] }} {{ __('messages.on_leave') }}
                        </small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
                @can('view admin_drivers')
                    <div class="mt-3">
                        <a href="{{ route('admin.drivers.index') }}" class="btn btn-sm btn-outline-success w-100">
                            {{ __('messages.manage_drivers') }}
                        </a>
                    </div>
                @endcan
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.monthly_revenue') }}</h6>
                        <h3 class="mb-0 fw-bold text-warning">${{ number_format($stats['monthly_revenue'], 2) }}</h3>
                        <small class="text-muted d-block">
                            @if (isset($stats['revenue_growth']) && $stats['revenue_growth'] > 0)
                                <i class="fas fa-arrow-up text-success"></i>
                                {{ $stats['revenue_growth'] }}%
                            @elseif(isset($stats['revenue_growth']) && $stats['revenue_growth'] < 0)
                                <i class="fas fa-arrow-down text-danger"></i>
                                {{ abs($stats['revenue_growth']) }}%
                            @else
                                <i class="fas fa-minus text-muted"></i>
                                {{ isset($stats['revenue_growth']) ? $stats['revenue_growth'] : 0 }}%
                            @endif
                            {{ __('messages.vs_last_month') }}
                        </small>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
                @can('view admin_financial')
                    <div class="mt-3">
                        <a href="{{ route('admin.financial.index') }}" class="btn btn-sm btn-outline-warning w-100">
                            {{ __('messages.view_financial') }}
                        </a>
                    </div>
                @endcan
            </div>
        </div>
    </div>
</div>

<!-- Secondary Statistics Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.total_students') }}</h6>
                        <h3 class="mb-0 fw-bold text-secondary">{{ number_format($stats['total_students']) }}</h3>
                        <small class="text-muted d-block">
                            <i class="fas fa-user-check text-success"></i>
                            {{ $stats['active_students'] }} {{ __('messages.active') }}
                        </small>
                        <small class="text-muted d-block">
                            <i class="fas fa-plus text-info"></i>
                            {{ $stats['new_students_this_month'] }} {{ __('messages.new_this_month') }}
                        </small>
                    </div>
                    <div class="text-secondary">
                        <i class="fas fa-graduation-cap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.active_subscriptions') }}</h6>
                        <h3 class="mb-0 fw-bold text-success">{{ number_format($stats['active_subscriptions']) }}</h3>
                        <small class="text-muted d-block">
                            <i class="fas fa-clock text-warning"></i>
                            {{ $stats['pending_subscriptions'] }} {{ __('messages.pending') }}
                        </small>
                        <small class="text-muted d-block">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                            {{ $stats['expiring_soon'] }} {{ __('messages.expiring_soon') }}
                        </small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.pending_payments') }}</h6>
                        <h3 class="mb-0 fw-bold text-danger">${{ number_format($stats['pending_payments'], 2) }}</h3>
                        <small class="text-muted d-block">
                            ${{ number_format($stats['overdue_payments'], 2) }}
                            {{ __('messages.overdue') }}
                        </small>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.total_revenue') }}</h6>
                        <h3 class="mb-0 fw-bold text-success">${{ number_format($stats['total_revenue'], 2) }}</h3>
                        <small class="text-muted d-block">{{ __('messages.all_time') }}</small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
                </div>

<!-- Additional Statistics Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.total_users') }}</h6>
                        <h3 class="mb-0 fw-bold text-info">{{ number_format($stats['total_users']) }}</h3>
                        <small class="text-muted d-block">
                            <i class="fas fa-sign-in-alt text-success"></i>
                            {{ $stats['users_logged_in_today'] }} {{ __('messages.logged_in_today') }}
                        </small>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-users-cog fa-2x"></i>
                    </div>
                </div>
                @can('view admin_users')
                    <div class="mt-3">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-outline-info w-100">
                            {{ __('messages.manage_users') }}
                        </a>
                    </div>
                @endcan
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.weekly_revenue') }}</h6>
                        <h3 class="mb-0 fw-bold text-success">${{ number_format($stats['weekly_revenue'], 2) }}</h3>
                        <small class="text-muted d-block">
                            @php
                                $weeklyGrowth = $stats['last_week_revenue'] > 0
                                    ? round((($stats['weekly_revenue'] - $stats['last_week_revenue']) / $stats['last_week_revenue']) * 100, 1)
                                    : ($stats['weekly_revenue'] > 0 ? 100 : 0);
                            @endphp
                            @if ($weeklyGrowth > 0)
                                <i class="fas fa-arrow-up text-success"></i>
                                {{ $weeklyGrowth }}%
                            @elseif($weeklyGrowth < 0)
                                <i class="fas fa-arrow-down text-danger"></i>
                                {{ abs($weeklyGrowth) }}%
                            @else
                                <i class="fas fa-minus text-muted"></i>
                                0%
                            @endif
                            {{ __('messages.vs_last_week') }}
                        </small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.system_activity') }}</h6>
                        <h3 class="mb-0 fw-bold text-primary">{{ number_format($stats['recent_audit_logs']) }}</h3>
                        <small class="text-muted d-block">
                            {{ __('messages.last_7_days') }}
                        </small>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-history fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted mb-2">{{ __('messages.failed_payments') }}</h6>
                        <h3 class="mb-0 fw-bold text-danger">{{ number_format($stats['failed_payments']) }}</h3>
                        <small class="text-muted d-block">
                            {{ __('messages.requires_attention') }}
                        </small>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
                </div>

<!-- Analytics Charts -->
<div class="row mb-4">
    <div class="col-lg-6 col-12 mb-3">
        <div class="card stats-card h-100">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.revenue_trend') }}</h6>
                <i class="fas fa-chart-line text-primary"></i>
            </div>
            <div class="card-body">
                <div class="chart-container w-100" style="position: relative; height: 300px;">
                    <canvas id="revenueChart" style="width: 100% !important; height: 100% !important;"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12 mb-3">
        <div class="card stats-card h-100">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.client_growth') }}</h6>
                <i class="fas fa-chart-area text-primary"></i>
            </div>
            <div class="card-body">
                <div class="chart-container w-100" style="position: relative; height: 300px;">
                    <canvas id="clientGrowthChart" style="width: 100% !important; height: 100% !important;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts Row -->
<div class="row mb-4">
    <div class="col-lg-6 col-12 mb-3">
        <div class="card stats-card h-100">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.subscription_status') }}</h6>
                <i class="fas fa-chart-pie text-primary"></i>
            </div>
            <div class="card-body">
                <div class="chart-container w-100" style="position: relative; height: 300px;">
                    <canvas id="subscriptionStatusChart" style="width: 100% !important; height: 100% !important;"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12 mb-3">
        <div class="card stats-card h-100">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.driver_status') }}</h6>
                <i class="fas fa-chart-donut text-primary"></i>
            </div>
            <div class="card-body">
                <div class="chart-container w-100" style="position: relative; height: 300px;">
                    <canvas id="driverStatusChart" style="width: 100% !important; height: 100% !important;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
                <div class="row g-3">
                    <div class="col-lg-8 col-12">
                        <div class="card shadow mb-4">
                            <div
                                class="card-header py-3 d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.recent_subscriptions') }}
                                </h6>
                                @can('view admin_subscriptions')
                                    <a href="{{ route('admin.subscriptions.index') }}"
                                        class="btn btn-sm btn-outline-primary">
                                        {{ __('messages.view_all') }}
                                    </a>
                                @endcan
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>{{ __('messages.client') }}</th>
                                                <th>{{ __('messages.route') }}</th>
                                                <th>{{ __('messages.type') }}</th>
                                                <th>{{ __('messages.status') }}</th>
                                                <th>{{ __('messages.created') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($recentActivities['recent_subscriptions'] as $subscription)
                                                <tr>
                                                    <td>{{ $subscription->client->name }}</td>
                                                    <td>{{ $subscription->route->route_name }}</td>
                                                    <td>
                                                        <span
                                                            class="badge bg-{{ $subscription->subscription_type == 'monthly' ? 'primary' : 'success' }}">
                                                            {{ __('messages.' . $subscription->subscription_type) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="badge bg-{{ $subscription->status == 'active' ? 'success' : 'warning' }}">
                                                            {{ __('messages.' . $subscription->status) }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center text-muted">
                                                        {{ __('messages.no_recent_subscriptions') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-12">
                        <div class="card shadow mb-4">
                            <div
                                class="card-header py-3 d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.recent_payments') }}</h6>
                                @can('view admin_payments')
                                    <a href="{{ route('admin.payments.index') }}" class="btn btn-sm btn-outline-primary">
                                        {{ __('messages.view_all') }}
                                    </a>
                                @endcan
                            </div>
                            <div class="card-body">
                                @forelse($recentActivities['recent_payments'] as $payment)
                                    <div class="d-flex align-items-center mb-3 p-2 border-bottom">
                                        <div class="me-3">
                                            <i
                                                class="fas fa-credit-card text-{{ $payment->status == 'completed' ? 'success' : 'warning' }}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">${{ number_format($payment->amount, 2) }}</div>
                                            <div class="text-muted small">{{ $payment->subscription->client->name }}</div>
                                            <div class="text-muted small">{{ $payment->created_at->diffForHumans() }}
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span
                                                class="badge bg-{{ $payment->status == 'completed' ? 'success' : 'warning' }}">
                                                {{ __('messages.' . $payment->status) }}
                                            </span>
                                        </div>
                                    </div>
                                @empty
                                    <p class="text-center text-muted">{{ __('messages.no_recent_payments') }}</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="row g-3 mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_actions') }}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    @can('create admin_clients')
                                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                            <a href="{{ route('admin.clients.create') }}"
                                                class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-user-plus me-2"></i>{{ __('messages.add_client') }}
                                            </a>
                                        </div>
                                    @endcan

                                    @can('create admin_routes')
                                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                            <a href="{{ route('admin.routes.create') }}"
                                                class="btn btn-info w-100 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-route me-2"></i>{{ __('messages.add_route') }}
                                            </a>
                                        </div>
                                    @endcan

                                    @can('create admin_drivers')
                                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                            <a href="{{ route('admin.drivers.create') }}"
                                                class="btn btn-success w-100 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-user-tie me-2"></i>{{ __('messages.add_driver') }}
                                            </a>
                                        </div>
                                    @endcan

                                    @can('view admin_financial')
                                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                            <a href="{{ route('admin.financial.index') }}"
                                                class="btn btn-warning w-100 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-chart-bar me-2"></i>{{ __('messages.view_financial') }}
                                            </a>
                                        </div>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Export Dashboard Function
function exportDashboard() {
    const exportBtn = event.target;
    const originalText = exportBtn.innerHTML;

    exportBtn.disabled = true;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.loading') }}';

    // Simulate export process
    setTimeout(function() {
        // Create export data
        const exportData = {
            timestamp: new Date().toISOString(),
            stats: {
                clients: {{ $stats['total_clients'] ?? 0 }},
                routes: {{ $stats['active_routes'] ?? 0 }},
                drivers: {{ $stats['active_drivers'] ?? 0 }},
                revenue: {{ $stats['monthly_revenue'] ?? 0 }}
            }
        };

        // Create and download file
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dashboard-export-' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);

        exportBtn.disabled = false;
        exportBtn.innerHTML = originalText;
    }, 1500);
}

document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['revenue_chart']['labels']),
            datasets: [{
                label: '{{ __("messages.revenue") }}',
                data: @json($chartData['revenue_chart']['data']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '{{ __("messages.revenue") }}: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Client Growth Chart
    const clientCtx = document.getElementById('clientGrowthChart').getContext('2d');
    new Chart(clientCtx, {
        type: 'bar',
        data: {
            labels: @json($chartData['client_growth_chart']['labels']),
            datasets: [{
                label: '{{ __("messages.new_clients") }}',
                data: @json($chartData['client_growth_chart']['data']),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '{{ __("messages.new_clients") }}: ' + context.parsed.y;
                        }
                    }
                }
            }
        }
    });

    // Subscription Status Chart (Pie Chart)
    const subscriptionCtx = document.getElementById('subscriptionStatusChart').getContext('2d');
    new Chart(subscriptionCtx, {
        type: 'pie',
        data: {
            labels: @json($chartData['subscription_status_chart']['labels']),
            datasets: [{
                data: @json($chartData['subscription_status_chart']['data']),
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',   // Active - Green
                    'rgba(255, 193, 7, 0.8)',   // Pending - Yellow
                    'rgba(220, 53, 69, 0.8)',   // Expired - Red
                    'rgba(108, 117, 125, 0.8)'  // Cancelled - Gray
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(108, 117, 125, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Driver Status Chart (Doughnut Chart)
    const driverCtx = document.getElementById('driverStatusChart').getContext('2d');
    new Chart(driverCtx, {
        type: 'doughnut',
        data: {
            labels: @json($chartData['driver_status_chart']['labels']),
            datasets: [{
                data: @json($chartData['driver_status_chart']['data']),
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',   // Active - Green
                    'rgba(255, 193, 7, 0.8)',   // On Leave - Yellow
                    'rgba(220, 53, 69, 0.8)',   // Inactive - Red
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)',
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
