<!doctype html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $messages['royal_transit'] ?? 'Royal Transit' }} -
        {{ $messages['student_transport_service'] ?? 'Student Transport Service' }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --main-bg: #f8f9fa;
            --main-text: #222;
            --primary: #002352;
            --accent: #bb943e;
            --section-bg: #f8f9fa;
            --card-bg: #fff;
            --footer-bg: #f8f9fa;
            --footer-border: #bb943e;
            --navbar-bg: #fff;
            --muted: #666;
        }

        body.dark-mode {
            --main-bg: #181c24;
            --main-text: #f8f9fa;
            --primary: #bb943e;
            --accent: #002352;
            --section-bg: #232a36;
            --card-bg: #232a36;
            --footer-bg: #181c24;
            --footer-border: #002352;
            --navbar-bg: #232a36;
            --muted: #e0e0e0;
        }

        html {
            scroll-padding-top: 70px;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            background: var(--main-bg);
            color: var(--main-text);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-section .logo {
            max-width: 120px;
            height: auto;
            margin-bottom: 20px;
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            height: 100%;
            background: var(--card-bg);
            color: var(--main-text);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 20px;
        }

        .btn-royal,
        .btn-auth-blue {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary) 100%);
            color: #fff !important;
            border: none;
        }

        .btn-auth-gold {
            background: linear-gradient(135deg, var(--accent) 0%, var(--accent) 100%);
            color: var(--main-text) !important;
            border: none;
        }

        .btn-auth-blue:hover,
        .btn-royal:hover {
            background: var(--primary);
            color: #fff !important;
        }

        .btn-auth-gold:hover {
            background: var(--accent);
            color: #fff !important;
        }

        .btn-auth-logout {
            background: #fff;
            color: #d32f2f !important;
            border: 1px solid #d32f2f;
        }

        .btn-auth-logout:hover {
            background: #d32f2f;
            color: #fff !important;
        }

        .section-title {
            position: relative;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            border-radius: 2px;
        }

        .stats-section {
            background: var(--section-bg);
            padding: 80px 0;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background: var(--navbar-bg) !important;
        }

        .navbar-light .navbar-nav .nav-link.active,
        .navbar-light .navbar-nav .nav-link:focus,
        .navbar-light .navbar-nav .nav-link:hover {
            color: var(--accent) !important;
        }

        .navbar-light .navbar-nav .nav-link {
            color: var(--primary) !important;
            font-weight: 500;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--primary) !important;
        }

        .footer {
            background: var(--footer-bg);
            color: var(--main-text);
            padding: 50px 0 20px;
            border-top: 3px solid var(--footer-border);
        }

        .footer h5,
        .footer a,
        .footer span {
            color: var(--primary) !important;
        }

        .footer .text-muted {
            color: var(--muted) !important;
        }

        .footer .text-dark {
            color: var(--main-text) !important;
        }

        .footer .text-decoration-underline {
            text-decoration: underline;
        }

        .text-muted {
            color: var(--muted) !important;
        }

        .text-dark {
            color: var(--main-text) !important;
        }

        .lang-switcher {
            position: fixed;
            z-index: 10000;
        }

        .lang-switcher {
            top: 18px;
            {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 20px;
        }

        @media (max-width: 576px) {
            .lang-switcher {
                top: 20px;
                {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 80px;
            }
        }

        .btn-lang {
            background: var(--card-bg);
            color: var(--primary) !important;
            border: 1px solid var(--primary);
        }

        .btn-lang:hover {
            background: var(--primary);
            color: #fff !important;
        }

        .btn-auth-gold {
            background: linear-gradient(135deg, var(--accent) 0%, var(--accent) 100%);
            color: var(--main-text) !important;
            border: none;
        }

        .btn-auth-gold:hover {
            background: var(--accent);
            color: #fff !important;
        }

        .section-title,
        .feature-card,
        .stats-section,
        .footer,
        section.bg-light {
            background: var(--section-bg) !important;
        }
    </style>
</head>

<body>
    <!-- Language Switcher -->
    <div class="lang-switcher">
        @if (app()->getLocale() == 'ar')
            <a class="btn btn-lang btn-sm" href="{{ route('language.switch', 'en') }}">
                <i class="fas fa-globe"></i> {{ __('english') }}
            </a>
        @else
            <a class="btn btn-lang btn-sm" href="{{ route('language.switch', 'ar') }}">
                <i class="fas fa-globe"></i> {{ __('arabic') }}
            </a>
        @endif
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="/images/logo.png" alt="Royal Transit Logo" style="height: 40px; width: auto;">
                <span class="d-none d-md-inline">{{ __('royal_transit') }}</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto px-2">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">{{ __('home') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">{{ __('services') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">{{ __('features') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">{{ __('contact') }}</a>
                    </li>
                </ul>

                <!-- Auth & Dark Mode Switchers -->
                <div class="d-flex align-items-center gap-2">
                    @auth
                        <a class="btn btn-auth-blue btn-sm me-2 mb-1" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> {{ __('dashboard') }}
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-auth-logout btn-sm mb-1">
                                <i class="fas fa-sign-out-alt"></i> {{ __('logout') }}
                            </button>
                        </form>
                    @else
                        <a class="btn btn-auth-blue btn-sm me-2 mb-1" href="{{ route('login') }}">
                            <i class="fas fa-sign-in-alt"></i> {{ __('login') }}
                        </a>
                        <a class="btn btn-auth-gold btn-sm mb-1" href="{{ route('register') }}">
                            <i class="fas fa-user-plus"></i> {{ __('register') }}
                        </a>
                    @endauth
                    <button id="darkModeToggle" class="btn btn-sm btn-outline-secondary darkmode-switcher">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="hero-section" id="home">
        <div class="container text-center">
            <img src="/images/logo.png" alt="Royal Transit Logo" class="logo">
            <h1 class="display-4">{{ __('welcome_to_royal_transit') }}</h1>
            <p class="lead">{{ __('your_reliable_transport_partner') }}</p>
            <a href="#services" class="btn btn-royal btn-lg">{{ __('explore_services') }}</a>
        </div>
    </header>

    <!-- Services Section -->
    <section class="py-5" id="services">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2>{{ __('our_services') }}</h2>
                <p class="text-muted">{{ __('services_description') }}</p>
            </div>
            <div class="row">
                <!-- Service Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-bus"></i>
                            </div>
                            <h5 class="card-title">{{ __('school_transport') }}</h5>
                            <p class="card-text text-muted">{{ __('safe_reliable_transport') }}</p>
                            <a href="#" class="btn btn-royal">{{ __('learn_more') }}</a>
                        </div>
                    </div>
                </div>
                <!-- Service Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <h5 class="card-title">{{ __('corporate_transport') }}</h5>
                            <p class="card-text text-muted">{{ __('professional_transport_solutions') }}</p>
                            <a href="#" class="btn btn-royal">{{ __('learn_more') }}</a>
                        </div>
                    </div>
                </div>
                <!-- Service Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-shuttle-van"></i>
                            </div>
                            <h5 class="card-title">{{ __('shuttle_services') }}</h5>
                            <p class="card-text text-muted">{{ __('convenient_shuttle_solutions') }}</p>
                            <a href="#" class="btn btn-royal">{{ __('learn_more') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5 bg-light" id="features">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2>{{ __('why_choose_us') }}</h2>
                <p class="text-muted">{{ __('features_description') }}</p>
            </div>
            <div class="row">
                <!-- Feature Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="card-title">{{ __('reliable_service') }}</h5>
                            <p class="card-text text-muted">{{ __('on_time_every_time') }}</p>
                        </div>
                    </div>
                </div>
                <!-- Feature Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <h5 class="card-title">{{ __('professional_drivers') }}</h5>
                            <p class="card-text text-muted">{{ __('experienced_careful_driving') }}</p>
                        </div>
                    </div>
                </div>
                <!-- Feature Item -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <h5 class="card-title">{{ __('customer_satisfaction') }}</h5>
                            <p class="card-text text-muted">{{ __('we_go_the_extra_mile') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-5" id="stats">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="stat-number">{{ $stats['completed_journeys'] }}</div>
                    <div class="text-muted">{{ __('completed_journeys') }}</div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="stat-number">{{ $stats['satisfied_clients'] }}</div>
                    <div class="text-muted">{{ __('satisfied_clients') }}</div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="stat-number">{{ $stats['years_experience'] }}</div>
                    <div class="text-muted">{{ __('years_experience') }}</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-5 bg-light" id="contact">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2>{{ __('contact_us') }}</h2>
                <p class="text-muted">{{ __('get_in_touch') }}</p>
            </div>
            <div class="row d-flex align-items-stretch">
                <div class="col-lg-6 mb-4 h-100 d-flex align-items-stretch">
                    <form action="{{ route('contact.submit') }}" method="POST" class="w-100">
                        @csrf
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('your_name') }}</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ __('your_email') }}</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">{{ __('your_message') }}</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-royal">
                            <i class="fas fa-paper-plane"></i> {{ __('send_message') }}
                        </button>
                    </form>
                </div>
                <div class="col-lg-6 h-100 d-flex align-items-stretch">
                    <div class="w-100">
                        <h5>{{ __('contact_information') }}</h5>
                        <p class="text-muted">
                            <i class="fas fa-map-marker-alt"></i> {{ __('main_address') }}<br>
                            <i class="fas fa-phone"></i> <a href="tel:01025994484" dir="ltr"
                                class="text-reset text-decoration-underline">010 25 99 4484</a><br>
                            <i class="fas fa-phone"></i> <a href="tel:01025994436" dir="ltr"
                                class="text-reset text-decoration-underline">010 25 99 4436</a><br>
                            <i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"
                                class="text-reset text-decoration-underline"><EMAIL></a>
                        </p>
                        <div class="mt-4">
                            <iframe src="https://www.google.com/maps?q=30.04396,30.96328&hl=ar;z=16&output=embed"
                                width="100%" height="250" style="border:0; border-radius:12px;" allowfullscreen=""
                                loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" style="background: #f8f9fa; color: #222;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4">
                    <h5>
                        <img src="/images/logo.png" alt="Royal Transit Logo"
                            style="height: 32px; width: auto; vertical-align: middle; margin-right: 8px;">
                        <span>Royal Transit</span>
                    </h5>
                    <p class="text-dark">{{ __('footer_description') }}</p>
                    <p class="text-dark mb-0">
                        <i class="fas fa-map-marker-alt"></i> {{ __('main_address') }}<br>
                        <i class="fas fa-phone"></i> <a href="tel:01025994484" dir="ltr"
                            class="text-dark text-decoration-underline">010 25 99 4484</a><br>
                        <i class="fas fa-phone"></i> <a href="tel:01025994436" dir="ltr"
                            class="text-dark text-decoration-underline">010 25 99 4436</a><br>
                        <i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"
                            class="text-dark text-decoration-underline"><EMAIL></a>
                    </p>
                </div>
                <div class="col-lg-4">
                    <h5>{{ __('quick_links') }}</h5>
                    <ul class="list-unstyled">
                        <li><a href="#home" class="text-muted">{{ __('home') }}</a></li>
                        <li><a href="#services" class="text-muted">{{ __('services') }}</a></li>
                        <li><a href="#features" class="text-muted">{{ __('features') }}</a></li>
                        <li><a href="#contact" class="text-muted">{{ __('contact') }}</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5>{{ __('follow_us') }}</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted"><i class="fab fa-facebook-f"></i> Facebook</a></li>
                        <li><a href="#" class="text-muted"><i class="fab fa-twitter"></i> Twitter</a></li>
                        <li><a href="#" class="text-muted"><i class="fab fa-instagram"></i> Instagram</a></li>
                        <li><a href="#" class="text-muted"><i class="fab fa-linkedin"></i> LinkedIn</a></li>
                    </ul>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col text-center">
                    <p class="text-muted mb-0">&copy; {{ date('Y') }} Royal Transit.
                        {{ __('all_rights_reserved') }}</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark mode toggle
        const toggle = document.getElementById('darkModeToggle');
        const body = document.body;
        if (localStorage.getItem('darkMode') === 'true') {
            body.classList.add('dark-mode');
        }
        toggle.addEventListener('click', function() {
            body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', body.classList.contains('dark-mode'));
        });
    </script>
</body>

</html>
