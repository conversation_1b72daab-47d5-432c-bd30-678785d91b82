<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('client_registration') }} - {{ __('royal_transit') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .lang-switcher {
            position: fixed;
            top: 20px;
            {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 20px;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <!-- Language Switcher -->
    <div class="lang-switcher">
        @if (app()->getLocale() == 'ar')
            <a class="btn btn-outline-dark btn-sm" href="{{ route('language.switch', 'en') }}">
                <i class="fas fa-globe"></i> {{ __('english') }}
            </a>
        @else
            <a class="btn btn-outline-dark btn-sm" href="{{ route('language.switch', 'ar') }}">
                <i class="fas fa-globe"></i> {{ __('arabic') }}
            </a>
        @endif
    </div>

    <div class="container mt-4 text-center">
        <!-- الهيدر -->
        <div class="brand-header d-flex justify-content-between align-items-center bg-danger text-white p-3 rounded">
            <div class="fw-bold fs-4 text-center">
                @if (app()->getLocale() == 'ar')
                    <a class="btn btn-outline-light btn-sm"
                        href="{{ url()->current() }}?lang=en">{{ __('english') }}</a>
                @else
                    <a class="btn btn-outline-light btn-sm"
                        href="{{ url()->current() }}?lang=ar">{{ __('arabic') }}</a>
                @endif
                | {{ __('royal_transit') }}
            </div>

            <div class="text-end">
                <p class="mb-1">{{ __('messages.call_center') }}:</p>
                <p class="mb-1" dir="ltr">
                    <a class="text-white text-decoration-underline" href="tel:@lang('phone1')">@lang('phone1')</a>
                </p>
                <p class="mb-0" dir="ltr">
                    <a class="text-white text-decoration-underline" href="tel:@lang('phone2')">@lang('phone2')</a>
                </p>
            </div>
        </div>
        <div class="mt-4 alert alert-success">
            ✅ {{ __('form_received_successfully') }}
        </div>

        @if (session('success'))
            <div class="alert alert-info">
                ✅ {{ session('success') }}
            </div>
        @endif

        @if (session('info'))
            <div class="alert alert-warning">
                ℹ️ {{ session('info') }}
            </div>
        @endif

        <a href="{{ route('clients.create') }}" class="btn btn-success">{{ __('back_to_form') }}</a>
    </div>
</body>

</html>
