@extends('layouts.admin')

@section('title', __('messages.route_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-route {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.route_management') }}
        </h1>
        @can('create admin_routes')
            <div class="btn-toolbar mb-2 mb-md-0">
                <a href="{{ route('admin.routes.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.add_route') }}
                </a>
            </div>
        @endcan
    </div>
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.total_routes') }}</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\TransportRoute::count() }}</h3>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-route fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.active_routes') }}</h6>
                            <h3 class="mb-0 fw-bold text-success">{{ \App\Models\TransportRoute::where('status', 'active')->count() }}</h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.total_capacity') }}</h6>
                            <h3 class="mb-0 fw-bold text-warning">{{ \App\Models\TransportRoute::sum('capacity') }}</h3>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.current_students') }}</h6>
                            <h3 class="mb-0 fw-bold text-info">{{ \App\Models\TransportRoute::sum('current_students') }}</h3>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.quick_actions') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="d-grid">
                                <button class="btn btn-outline-primary" onclick="showCapacityReport()">
                                    <i class="fas fa-chart-pie {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.capacity_report') }}
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-grid">
                                <button class="btn btn-outline-success" onclick="showDriverAssignments()">
                                    <i class="fas fa-user-tie {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.driver_assignments') }}
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-grid">
                                <button class="btn btn-outline-warning" onclick="showScheduleOverview()">
                                    <i class="fas fa-clock {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.schedule_overview') }}
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-info">
                                    <i class="fas fa-download {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.export_routes') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- DataTable -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.routes_list') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Capacity Report Modal -->
<div class="modal fade" id="capacityReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-pie {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.route_capacity_report') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="capacityReportContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">{{ __('messages.loading') }}...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
function showCapacityReport() {
    fetch('{{ route("admin.routes.capacity-report") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Route</th><th>Capacity</th><th>Current Students</th><th>Utilization</th><th>Available Seats</th></tr></thead><tbody>';
                data.data.forEach(route => {
                    html += `<tr>
                        <td>${route.name}</td>
                        <td>${route.capacity}</td>
                        <td>${route.current_students}</td>
                        <td>${route.utilization}%</td>
                        <td>${route.available_seats}</td>
                    </tr>`;
                });
                html += '</tbody></table></div>';

                Swal.fire({
                    title: '{{ __("messages.capacity_report") }}',
                    html: html,
                    width: '80%',
                    showCloseButton: true
                });
            }
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to load capacity report', 'error');
        });
}

function showDriverAssignments() {
    fetch('{{ route("admin.routes.driver-assignments") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Route</th><th>Driver</th><th>Assigned Date</th><th>Status</th></tr></thead><tbody>';
                data.data.forEach(assignment => {
                    html += `<tr>
                        <td>${assignment.route_name}</td>
                        <td>${assignment.driver_name || 'Unassigned'}</td>
                        <td>${assignment.assigned_date || 'N/A'}</td>
                        <td><span class="badge ${assignment.status === 'assigned' ? 'bg-success' : 'bg-warning'}">${assignment.status}</span></td>
                    </tr>`;
                });
                html += '</tbody></table></div>';

                Swal.fire({
                    title: '{{ __("messages.driver_assignments") }}',
                    html: html,
                    width: '80%',
                    showCloseButton: true
                });
            }
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to load driver assignments', 'error');
        });
}

function showScheduleOverview() {
    fetch('{{ route("admin.routes.schedule-overview") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Route</th><th>Pickup Time</th><th>Dropoff Time</th><th>From</th><th>To</th><th>Students</th><th>Driver</th></tr></thead><tbody>';
                data.data.forEach(schedule => {
                    html += `<tr>
                        <td>${schedule.route_name}</td>
                        <td>${schedule.pickup_time}</td>
                        <td>${schedule.dropoff_time}</td>
                        <td>${schedule.from_area}</td>
                        <td>${schedule.to_school}</td>
                        <td>${schedule.students_count}</td>
                        <td>${schedule.driver}</td>
                    </tr>`;
                });
                html += '</tbody></table></div>';

                Swal.fire({
                    title: '{{ __("messages.schedule_overview") }}',
                    html: html,
                    width: '90%',
                    showCloseButton: true
                });
            }
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to load schedule overview', 'error');
        });
}
</script>
@endpush
@endsection

@push('styles')
<style>
    .stats-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: transform 0.2s ease-in-out;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .table-responsive {
        border-radius: 0.375rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
        white-space: nowrap;
    }

    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-warning:hover,
    .btn-outline-info:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    @media (max-width: 768px) {
        .btn-toolbar {
            width: 100%;
            justify-content: center;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
            text-align: center;
        }

        .d-flex.justify-content-between h1 {
            margin-bottom: 1rem;
        }

        .row.g-3 .col-lg-3 {
            margin-bottom: 0.5rem;
        }
    }
</style>
@endpush
