@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar-custom sidebar">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="/images/logo.png" alt="Royal Transit Logo" style="max-width: 90px; height: auto; margin-bottom: 10px;">
                    <h5 class="mt-2">Royal Transit</h5>
                    <p class="text-muted small">Admin Dashboard</p>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    @can('read admin_clients')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.clients.index') }}">
                            <i class="fas fa-users me-2"></i>Clients
                        </a>
                    </li>
                    @endcan
                    @can('read admin_routes')
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.routes.index') }}">
                            <i class="fas fa-route me-2"></i>Routes
                        </a>
                    </li>
                    @endcan
                    @can('read admin_drivers')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.drivers.index') }}">
                            <i class="fas fa-car me-2"></i>Drivers
                        </a>
                    </li>
                    @endcan
                    @can('read admin_financial_reports')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.financial.index') }}">
                            <i class="fas fa-chart-bar me-2"></i>Financial
                        </a>
                    </li>
                    @endcan
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.subscriptions.index') }}">
                            <i class="fas fa-clipboard-list me-2"></i>Subscriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.payments.index') }}">
                            <i class="fas fa-credit-card me-2"></i>Payments
                        </a>
                    </li>
                </ul>
                
                <hr>
                
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" 
                       id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-2"></i>
                        <strong>{{ Auth::user()->name }}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                        <li><a class="dropdown-item" href="{{ route('profile.show') }}">Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">Sign out</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-plus-circle me-2"></i>Add New Route
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.routes.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Routes
                    </a>
                </div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.routes.store') }}">
                @csrf
                
                <div class="row">
                    <!-- Route Information -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-route me-2"></i>Route Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Route Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required
                                           placeholder="e.g., Malqa - Al Rawad Schools">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="from_area" class="form-label">From Area <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('from_area') is-invalid @enderror" 
                                                   id="from_area" name="from_area" value="{{ old('from_area') }}" required
                                                   placeholder="e.g., Malqa District">
                                            @error('from_area')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="to_school" class="form-label">To School <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('to_school') is-invalid @enderror" 
                                                   id="to_school" name="to_school" value="{{ old('to_school') }}" required
                                                   placeholder="e.g., Al Rawad Private Schools">
                                            @error('to_school')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="pickup_time" class="form-label">Pickup Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control @error('pickup_time') is-invalid @enderror" 
                                                   id="pickup_time" name="pickup_time" value="{{ old('pickup_time') }}" required>
                                            @error('pickup_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dropoff_time" class="form-label">Dropoff Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control @error('dropoff_time') is-invalid @enderror" 
                                                   id="dropoff_time" name="dropoff_time" value="{{ old('dropoff_time') }}" required>
                                            @error('dropoff_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="monthly_price" class="form-label">Monthly Price (SAR) <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">SAR</span>
                                                <input type="number" class="form-control @error('monthly_price') is-invalid @enderror" 
                                                       id="monthly_price" name="monthly_price" value="{{ old('monthly_price') }}" 
                                                       required min="0" step="0.01" placeholder="400.00">
                                                @error('monthly_price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">Capacity <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                                   id="capacity" name="capacity" value="{{ old('capacity', 30) }}" 
                                                   required min="1" placeholder="30">
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3" 
                                              placeholder="Optional description of the route">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Route Stops -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>Route Stops
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="stops-container">
                                    <div class="stop-item mb-3">
                                        <label class="form-label">Stop 1</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="stops[]" 
                                                   placeholder="Enter stop location" value="{{ old('stops.0') }}">
                                            <button type="button" class="btn btn-outline-danger" onclick="removeStop(this)" disabled>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="button" class="btn btn-outline-primary" onclick="addStop()">
                                    <i class="fas fa-plus me-1"></i>Add Stop
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Settings -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="driver_id" class="form-label">Assign Driver</label>
                                    <select class="form-select @error('driver_id') is-invalid @enderror" 
                                            id="driver_id" name="driver_id">
                                        <option value="">Select Driver (Optional)</option>
                                        @foreach(\App\Models\Driver::active()->whereDoesntHave('activeRoutes')->get() as $driver)
                                            <option value="{{ $driver->id }}" {{ old('driver_id') == $driver->id ? 'selected' : '' }}>
                                                {{ $driver->name }} - {{ $driver->phone }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('driver_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Only drivers without active route assignments are shown</div>
                                </div>
                            </div>
                        </div>

                        <!-- Route Preview -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-eye me-2"></i>Route Preview
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="route-preview">
                                    <div class="mb-2">
                                        <strong>Route:</strong> <span id="preview-name">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>From:</strong> <span id="preview-from">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>To:</strong> <span id="preview-to">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Schedule:</strong> <span id="preview-schedule">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Price:</strong> <span id="preview-price">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Capacity:</strong> <span id="preview-capacity">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ route('admin.routes.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-main">
                        <i class="fas fa-save me-1"></i>Create Route
                    </button>
                </div>
            </form>
        </main>
    </div>
</div>
@endsection

@push('scripts')
<script>
let stopCount = 1;

function addStop() {
    stopCount++;
    const container = document.getElementById('stops-container');
    const stopItem = document.createElement('div');
    stopItem.className = 'stop-item mb-3';
    stopItem.innerHTML = `
        <label class="form-label">Stop ${stopCount}</label>
        <div class="input-group">
            <input type="text" class="form-control" name="stops[]" placeholder="Enter stop location">
            <button type="button" class="btn btn-outline-danger" onclick="removeStop(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(stopItem);
    updateRemoveButtons();
}

function removeStop(button) {
    button.closest('.stop-item').remove();
    updateStopNumbers();
    updateRemoveButtons();
}

function updateStopNumbers() {
    const stopItems = document.querySelectorAll('.stop-item');
    stopItems.forEach((item, index) => {
        item.querySelector('label').textContent = `Stop ${index + 1}`;
    });
    stopCount = stopItems.length;
}

function updateRemoveButtons() {
    const removeButtons = document.querySelectorAll('.stop-item .btn-outline-danger');
    removeButtons.forEach((button, index) => {
        button.disabled = removeButtons.length === 1;
    });
}

// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const fields = {
        'name': 'preview-name',
        'from_area': 'preview-from',
        'to_school': 'preview-to',
        'monthly_price': 'preview-price',
        'capacity': 'preview-capacity'
    };
    
    Object.keys(fields).forEach(fieldName => {
        const field = document.getElementById(fieldName);
        const preview = document.getElementById(fields[fieldName]);
        
        field.addEventListener('input', function() {
            let value = this.value || '-';
            if (fieldName === 'monthly_price' && value !== '-') {
                value = 'SAR ' + parseFloat(value).toFixed(2);
            }
            if (fieldName === 'capacity' && value !== '-') {
                value = value + ' students';
            }
            preview.textContent = value;
        });
    });
    
    // Schedule preview
    const pickupTime = document.getElementById('pickup_time');
    const dropoffTime = document.getElementById('dropoff_time');
    const schedulePreview = document.getElementById('preview-schedule');
    
    function updateSchedule() {
        const pickup = pickupTime.value;
        const dropoff = dropoffTime.value;
        if (pickup && dropoff) {
            schedulePreview.textContent = `${pickup} - ${dropoff}`;
        } else {
            schedulePreview.textContent = '-';
        }
    }
    
    pickupTime.addEventListener('change', updateSchedule);
    dropoffTime.addEventListener('change', updateSchedule);
});
</script>
@endpush

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .route-preview {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }
    
    .stop-item {
        position: relative;
    }
    
    .stop-item:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 20px;
        bottom: -15px;
        width: 2px;
        height: 15px;
        background-color: #dee2e6;
    }
</style>
@endpush
