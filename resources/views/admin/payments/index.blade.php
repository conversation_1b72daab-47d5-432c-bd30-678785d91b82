@extends('layouts.admin')

@section('title', __('messages.payment_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ __('messages.payment_management') }}</h1>
        @can('create admin_payments')
            <a href="{{ route('admin.payments.create') }}" class="btn btn-primary">
                <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                {{ __('messages.add_payment') }}
            </a>
        @endcan
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.total_payments') }}</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_payments']) }}</h3>
                        </div>
                        <i class="fas fa-credit-card fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.pending_payments') }}</h6>
                            <h3 class="mb-0">{{ number_format($stats['pending_payments']) }}</h3>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.completed_payments') }}</h6>
                            <h3 class="mb-0">{{ number_format($stats['completed_payments']) }}</h3>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.total_amount') }}</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_amount'], 2) }} {{ __('messages.egp') }}</h3>
                        </div>
                        <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.payments') }}</h6>
        </div>
        <div class="card-body">
            {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
        </div>
    </div>
</div>
@endsection

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
$(document).ready(function() {
    // Handle status update
    $('.update-status').on('click', function() {
        const paymentId = $(this).data('payment-id');
        const currentStatus = $(this).data('current-status');
        
        // Show status update modal or form
        // Implementation depends on your UI preferences
    });
    
    // Handle bulk actions
    $('.bulk-action').on('click', function() {
        const action = $(this).data('action');
        const selectedIds = [];
        
        $('.payment-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        if (selectedIds.length === 0) {
            alert('{{ __("messages.please_select_payments") }}');
            return;
        }
        
        // Process bulk action
        // Implementation depends on your requirements
    });
});
</script>
@endpush
