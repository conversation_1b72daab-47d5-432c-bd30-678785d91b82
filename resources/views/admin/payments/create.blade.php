@extends('layouts.admin')

@section('title', __('messages.add_payment'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ __('messages.add_payment') }}</h1>
        <a href="{{ route('admin.payments.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ __('messages.back') }}
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.payment_details') }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.payments.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="subscription_id" class="form-label">{{ __('messages.subscription') }} <span class="text-danger">*</span></label>
                                <select class="form-select @error('subscription_id') is-invalid @enderror" id="subscription_id" name="subscription_id" required>
                                    <option value="">{{ __('messages.select_subscription') }}</option>
                                    @foreach($subscriptions as $subscription)
                                        <option value="{{ $subscription->id }}" {{ old('subscription_id') == $subscription->id ? 'selected' : '' }}>
                                            {{ $subscription->client->user->name }} - {{ $subscription->route->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('subscription_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_reference" class="form-label">{{ __('messages.payment_reference') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                       id="payment_reference" name="payment_reference" value="{{ old('payment_reference', 'PAY-' . strtoupper(Str::random(8))) }}" required>
                                @error('payment_reference')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">{{ __('messages.amount') }} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control @error('amount') is-invalid @enderror" 
                                           id="amount" name="amount" value="{{ old('amount') }}" required>
                                    <span class="input-group-text">{{ __('messages.egp') }}</span>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">{{ __('messages.payment_method') }} <span class="text-danger">*</span></label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method" required>
                                    <option value="">{{ __('messages.select_payment_method') }}</option>
                                    <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>{{ __('messages.cash') }}</option>
                                    <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>{{ __('messages.bank_transfer') }}</option>
                                    <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>{{ __('messages.credit_card') }}</option>
                                    <option value="mobile_wallet" {{ old('payment_method') == 'mobile_wallet' ? 'selected' : '' }}>{{ __('messages.mobile_wallet') }}</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_date" class="form-label">{{ __('messages.payment_date') }} <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                       id="payment_date" name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                @error('payment_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">{{ __('messages.due_date') }}</label>
                                <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                       id="due_date" name="due_date" value="{{ old('due_date') }}">
                                @error('due_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">{{ __('messages.status') }} <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>{{ __('messages.pending') }}</option>
                                    <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>{{ __('messages.completed') }}</option>
                                    <option value="failed" {{ old('status') == 'failed' ? 'selected' : '' }}>{{ __('messages.failed') }}</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">{{ __('messages.notes') }}</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="payment_details" class="form-label">{{ __('messages.payment_details') }}</label>
                            <textarea class="form-control @error('payment_details') is-invalid @enderror" id="payment_details" name="payment_details" rows="2" placeholder="{{ __('messages.additional_payment_details') }}">{{ old('payment_details') }}</textarea>
                            @error('payment_details')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" onclick="window.history.back()">
                                {{ __('messages.cancel') }}
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                {{ __('messages.save_payment') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_client_search') }}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="client_search" class="form-label">{{ __('messages.search_client') }}</label>
                        <select class="form-select" id="client_search">
                            <option value="">{{ __('messages.select_client') }}</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}" data-subscriptions="{{ $client->subscriptions->toJson() }}">
                                    {{ $client->user->name }} - {{ $client->user->phone }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div id="client-subscriptions" class="d-none">
                        <h6>{{ __('messages.client_subscriptions') }}</h6>
                        <div id="subscriptions-list"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Client search functionality
    $('#client_search').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const subscriptions = selectedOption.data('subscriptions');
        
        if (subscriptions && subscriptions.length > 0) {
            let subscriptionsList = '';
            subscriptions.forEach(function(subscription) {
                subscriptionsList += `
                    <div class="border rounded p-2 mb-2">
                        <strong>${subscription.route.name}</strong><br>
                        <small class="text-muted">
                            ${subscription.status} - ${subscription.route.monthly_price} {{ __('messages.egp') }}
                        </small>
                        <button type="button" class="btn btn-sm btn-outline-primary float-end select-subscription" 
                                data-subscription-id="${subscription.id}" 
                                data-amount="${subscription.route.monthly_price}">
                            {{ __('messages.select') }}
                        </button>
                    </div>
                `;
            });
            
            $('#subscriptions-list').html(subscriptionsList);
            $('#client-subscriptions').removeClass('d-none');
        } else {
            $('#client-subscriptions').addClass('d-none');
        }
    });
    
    // Select subscription
    $(document).on('click', '.select-subscription', function() {
        const subscriptionId = $(this).data('subscription-id');
        const amount = $(this).data('amount');
        
        $('#subscription_id').val(subscriptionId);
        $('#amount').val(amount);
    });
});
</script>
@endpush
