@extends('layouts.admin')

@section('title', __('messages.payment_details'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ __('messages.payment_details') }}</h1>
        <div>
            <a href="{{ route('admin.payments.index') }}" class="btn btn-secondary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                {{ __('messages.back') }}
            </a>
            @can('update admin_payments')
                <a href="{{ route('admin.payments.edit', $payment) }}" class="btn btn-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                    <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ __('messages.edit') }}
                </a>
            @endcan
            @if($payment->status === 'completed')
                <a href="{{ route('admin.payments.invoice', $payment) }}" class="btn btn-info" target="_blank">
                    <i class="fas fa-file-invoice {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                    {{ __('messages.generate_invoice') }}
                </a>
            @endif
        </div>
    </div>

    <div class="row">
        <!-- Payment Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.payment_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.payment_reference') }}</label>
                            <p class="form-control-plaintext">{{ $payment->payment_reference }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.amount') }}</label>
                            <p class="form-control-plaintext">
                                <span class="h5 text-success">{{ number_format($payment->amount, 2) }} {{ __('messages.egp') }}</span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.payment_method') }}</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.status') }}</label>
                            <p class="form-control-plaintext">
                                @switch($payment->status)
                                    @case('completed')
                                        <span class="badge bg-success">{{ __('messages.completed') }}</span>
                                        @break
                                    @case('pending')
                                        <span class="badge bg-warning">{{ __('messages.pending') }}</span>
                                        @break
                                    @case('failed')
                                        <span class="badge bg-danger">{{ __('messages.failed') }}</span>
                                        @break
                                    @case('refunded')
                                        <span class="badge bg-secondary">{{ __('messages.refunded') }}</span>
                                        @break
                                    @default
                                        <span class="badge bg-light text-dark">{{ ucfirst($payment->status) }}</span>
                                @endswitch
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.payment_date') }}</label>
                            <p class="form-control-plaintext">{{ $payment->payment_date ? $payment->payment_date->format('Y-m-d') : '-' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.due_date') }}</label>
                            <p class="form-control-plaintext">
                                {{ $payment->due_date ? $payment->due_date->format('Y-m-d') : '-' }}
                                @if($payment->due_date && $payment->due_date->isPast() && $payment->status === 'pending')
                                    <span class="badge bg-danger {{ app()->getLocale() == 'ar' ? 'me-2' : 'ms-2' }}">{{ __('messages.overdue') }}</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    @if($payment->notes)
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.notes') }}</label>
                            <p class="form-control-plaintext">{{ $payment->notes }}</p>
                        </div>
                    @endif

                    @if($payment->payment_details)
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('messages.payment_details') }}</label>
                            <p class="form-control-plaintext">{{ $payment->payment_details }}</p>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.created_at') }}</label>
                            <p class="form-control-plaintext">{{ $payment->created_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.updated_at') }}</label>
                            <p class="form-control-plaintext">{{ $payment->updated_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.subscription_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.client') }}</label>
                            <p class="form-control-plaintext">
                                <a href="{{ route('admin.clients.show', $payment->subscription->client) }}" class="text-decoration-none">
                                    {{ $payment->subscription->client->user->name }}
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.route') }}</label>
                            <p class="form-control-plaintext">
                                <a href="{{ route('admin.routes.show', $payment->subscription->route) }}" class="text-decoration-none">
                                    {{ $payment->subscription->route->name }}
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.subscription_status') }}</label>
                            <p class="form-control-plaintext">
                                @switch($payment->subscription->status)
                                    @case('active')
                                        <span class="badge bg-success">{{ __('messages.active') }}</span>
                                        @break
                                    @case('inactive')
                                        <span class="badge bg-secondary">{{ __('messages.inactive') }}</span>
                                        @break
                                    @case('suspended')
                                        <span class="badge bg-warning">{{ __('messages.suspended') }}</span>
                                        @break
                                    @default
                                        <span class="badge bg-light text-dark">{{ ucfirst($payment->subscription->status) }}</span>
                                @endswitch
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ __('messages.subscription_price') }}</label>
                            <p class="form-control-plaintext">{{ number_format($payment->subscription->price, 2) }} {{ __('messages.egp') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Panel -->
        <div class="col-lg-4">
            @can('update admin_payments')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_actions') }}</h6>
                    </div>
                    <div class="card-body">
                        @if($payment->status === 'pending')
                            <form action="{{ route('admin.payments.update-status', $payment) }}" method="POST" class="mb-2">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="completed">
                                <button type="submit" class="btn btn-success w-100 mb-2" onclick="return confirm('{{ __('messages.mark_payment_completed_confirm') }}')">
                                    <i class="fas fa-check {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.mark_as_completed') }}
                                </button>
                            </form>

                            <form action="{{ route('admin.payments.update-status', $payment) }}" method="POST" class="mb-2">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="failed">
                                <button type="submit" class="btn btn-danger w-100" onclick="return confirm('{{ __('messages.mark_payment_failed_confirm') }}')">
                                    <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.mark_as_failed') }}
                                </button>
                            </form>
                        @endif

                        @if($payment->status === 'completed')
                            <form action="{{ route('admin.payments.update-status', $payment) }}" method="POST" class="mb-2">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="refunded">
                                <button type="submit" class="btn btn-warning w-100" onclick="return confirm('{{ __('messages.refund_payment_confirm') }}')">
                                    <i class="fas fa-undo {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.refund_payment') }}
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            @endcan

            <!-- Payment History -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.payment_history') }}</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ __('messages.payment_created') }}</h6>
                                <p class="timeline-text">{{ $payment->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        
                        @if($payment->payment_date)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ __('messages.payment_processed') }}</h6>
                                    <p class="timeline-text">{{ $payment->payment_date->format('Y-m-d H:i:s') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($payment->updated_at != $payment->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ __('messages.payment_updated') }}</h6>
                                    <p class="timeline-text">{{ $payment->updated_at->format('Y-m-d H:i:s') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
@endpush
