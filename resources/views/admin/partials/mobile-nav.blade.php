<!-- Mobile Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary d-lg-none fixed-top">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" href="{{ route('admin.dashboard') }}">
            <img src="{{ asset('assets/img/logo-white.png') }}" alt="Royal Transit" height="30" class="{{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
            <span class="fw-bold">{{ config('app.name') }}</span>
        </a>

        <!-- Mobile Menu Toggle -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNavMenu" aria-controls="mobileNavMenu" aria-expanded="false" aria-label="{{ __('messages.toggle_navigation') }}">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Mobile Menu -->
        <div class="collapse navbar-collapse" id="mobileNavMenu">
            <ul class="navbar-nav {{ app()->getLocale() == 'ar' ? 'me-auto' : 'ms-auto' }}">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        {{ __('messages.dashboard') }}
                    </a>
                </li>

                <!-- Business Management -->
                @canany(['view admin_clients', 'view admin_routes', 'view admin_drivers'])
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="businessDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-business-time {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.business_management') }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="businessDropdown">
                            @can('view admin_clients')
                                <li><a class="dropdown-item" href="{{ route('admin.clients.index') }}">
                                    <i class="fas fa-users {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.clients') }}
                                </a></li>
                            @endcan
                            @can('view admin_routes')
                                <li><a class="dropdown-item" href="{{ route('admin.routes.index') }}">
                                    <i class="fas fa-route {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.routes') }}
                                </a></li>
                            @endcan
                            @can('view admin_drivers')
                                <li><a class="dropdown-item" href="{{ route('admin.drivers.index') }}">
                                    <i class="fas fa-user-tie {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.drivers') }}
                                </a></li>
                            @endcan
                        </ul>
                    </li>
                @endcanany

                <!-- Financial Management -->
                @canany(['view admin_payments', 'view admin_financial'])
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="financialDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-line {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.financial_management') }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="financialDropdown">
                            @can('view admin_payments')
                                <li><a class="dropdown-item" href="{{ route('admin.payments.index') }}">
                                    <i class="fas fa-credit-card {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.payments') }}
                                </a></li>
                            @endcan
                            @can('view admin_financial')
                                <li><a class="dropdown-item" href="{{ route('admin.financial.index') }}">
                                    <i class="fas fa-chart-bar {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.financial_reports') }}
                                </a></li>
                            @endcan
                        </ul>
                    </li>
                @endcanany

                <!-- System Management -->
                @canany(['read users', 'read roles'])
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="systemDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cogs {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.system_management') }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="systemDropdown">
                            @can('read users')
                                <li><a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                    <i class="fas fa-users-cog {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.users') }}
                                </a></li>
                            @endcan
                            @can('read roles')
                                <li><a class="dropdown-item" href="{{ route('admin.roles.index') }}">
                                    <i class="fas fa-user-shield {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.roles') }}
                                </a></li>
                            @endcan
                        </ul>
                    </li>
                @endcanany

                <!-- Language Switcher -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-globe {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        {{ app()->getLocale() == 'ar' ? 'العربية' : 'English' }}
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                        <li><a class="dropdown-item" href="{{ route('language.switch', 'en') }}">
                            <i class="fas fa-flag-usa {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            English
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('language.switch', 'ar') }}">
                            <i class="fas fa-flag {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            العربية
                        </a></li>
                    </ul>
                </li>

                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="{{ Auth::user()->profile_photo_url }}" alt="{{ Auth::user()->name }}" class="rounded-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" width="24" height="24">
                        {{ Auth::user()->name }}
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="{{ route('profile.show') }}">
                            <i class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.profile') }}
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.logout') }}
                                </button>
                            </form>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* Mobile Navigation Styles */
@media (max-width: 991.98px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin: 0.125rem 0;
    }
    
    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: 600;
    }
    
    .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .dropdown-item {
        padding: 0.5rem 1rem;
        transition: background-color 0.15s ease-in-out;
    }
    
    .dropdown-item:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
    
    /* Adjust main content for mobile nav */
    .main-content {
        padding-top: 5rem !important;
    }
}
</style>
