<!-- Sidebar -->
<nav class="sidebar-custom sidebar" id="sidebar">
    <div class="position-sticky pt-3">
        <!-- <PERSON><PERSON> and <PERSON> -->
        <div class="text-center mb-4 px-3">
            <div class="mb-3">
                <img src="/images/logo.png" alt="{{ __('messages.royal_transit') }} {{ __('messages.logo') }}"
                    class="img-fluid" style="max-width: 80px; height: auto;">
            </div>
            <h5 class="text-white mb-1 fw-bold">{{ __('messages.royal_transit') }}</h5>
            <p class="text-white-50 small mb-0">{{ __('messages.admin_panel') }}</p>
        </div>

        <!-- Navigation Divider -->
        <hr class="sidebar-divider my-3" style="border-color: rgba(255,255,255,0.2);">

        <!-- Main Navigation -->
        <div class="sidebar-content px-2">

            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
                        href="{{ route('dashboard') }}"
                        data-bs-toggle="tooltip"
                        data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                        title="{{ __('messages.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>{{ __('messages.dashboard') }}</span>
                    </a>
                </li>

                <!-- Business Management -->
                @canany(['view admin_clients', 'view admin_routes', 'view admin_drivers', 'view admin_financial'])
                    <li class="nav-item mt-3">
                        <h6 class="nav-heading text-white-50 text-uppercase small fw-bold px-3 mb-2">
                            {{ __('messages.business_management') }}
                        </h6>
                    </li>
                @endcanany

                @can('view admin_clients')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.clients.*') ? 'active' : '' }}"
                            href="{{ route('admin.clients.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.clients') }}">
                            <i class="fas fa-users"></i>
                            <span>{{ __('messages.clients') }}</span>
                        </a>
                    </li>
                @endcan

                @can('view admin_routes')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.routes.*') ? 'active' : '' }}"
                            href="{{ route('admin.routes.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.routes') }}">
                            <i class="fas fa-route"></i>
                            <span>{{ __('messages.routes') }}</span>
                        </a>
                    </li>
                @endcan

                @can('view admin_drivers')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.drivers.*') ? 'active' : '' }}"
                            href="{{ route('admin.drivers.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.drivers') }}">
                            <i class="fas fa-car"></i>
                            <span>{{ __('messages.drivers') }}</span>
                        </a>
                    </li>
                @endcan

                @can('view admin_financial')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.financial.*') ? 'active' : '' }}"
                            href="{{ route('admin.financial.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.financial') }}">
                            <i class="fas fa-chart-bar"></i>
                            <span>{{ __('messages.financial') }}</span>
                        </a>
                    </li>
                @endcan

                <!-- Subscription Management -->
                @canany(['view admin_subscriptions', 'view admin_payments'])
                    <li class="nav-item mt-3">
                        <h6 class="nav-heading text-white-50 text-uppercase small fw-bold px-3 mb-2">
                            {{ __('messages.subscription_management') }}
                        </h6>
                    </li>
                @endcanany

                @can('view admin_subscriptions')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.subscriptions.*') ? 'active' : '' }}"
                            href="{{ route('admin.subscriptions.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.subscriptions') }}">
                            <i class="fas fa-clipboard-list"></i>
                            <span>{{ __('messages.subscriptions') }}</span>
                        </a>
                    </li>
                @endcan

                @can('view admin_payments')
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.payments.*') ? 'active' : '' }}"
                            href="{{ route('admin.payments.index') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                            title="{{ __('messages.payments') }}">
                            <i class="fas fa-credit-card"></i>
                            <span>{{ __('messages.payments') }}</span>
                        </a>
                    </li>
                @endcan
            </ul>
        </div>

        <!-- System Management Section -->
        @canany(['read users', 'read roles', 'read permissions', 'read translations'])
            <div class="px-2">
                <hr class="sidebar-divider my-3" style="border-color: rgba(255,255,255,0.2);">
                <h6 class="nav-heading text-white-50 text-uppercase small fw-bold px-3 mb-2">
                    {{ __('messages.system_management') }}
                </h6>
                <ul class="nav flex-column">
                    @can('read users')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                                href="{{ route('admin.users.index') }}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                                title="{{ __('messages.user_management') }}">
                                <i class="fas fa-users-cog"></i>
                                <span>{{ __('messages.user_management') }}</span>
                            </a>
                        </li>
                    @endcan
                    @can('read roles')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}"
                                href="{{ route('admin.roles.index') }}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                                title="{{ __('messages.role_management') }}">
                                <i class="fas fa-user-shield"></i>
                                <span>{{ __('messages.role_management') }}</span>
                            </a>
                        </li>
                    @endcan
                    @can('read permissions')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.permissions.*') ? 'active' : '' }}"
                                href="{{ route('admin.permissions.index') }}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                                title="{{ __('messages.permission_management') }}">
                                <i class="fas fa-key"></i>
                                <span>{{ __('messages.permission_management') }}</span>
                            </a>
                        </li>
                    @endcan
                    @can('read translations')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.translations.*') ? 'active' : '' }}"
                                href="{{ route('admin.translations.index') }}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="{{ app()->getLocale() == 'ar' ? 'left' : 'right' }}"
                                title="{{ __('messages.translation_management') }}">
                                <i class="fas fa-language"></i>
                                <span>{{ __('messages.translation_management') }}</span>
                            </a>
                        </li>
                    @endcan
                </ul>
            </div>
        @endcanany
        </div>

        <!-- Bottom Section -->
        <div class="mt-auto px-3 pb-3">
            <hr class="sidebar-divider my-3" style="border-color: rgba(255,255,255,0.2);">

            <!-- Language Switcher -->
            <div class="mb-3">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle w-100 d-flex align-items-center justify-content-between"
                            type="button" id="languageDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="d-flex align-items-center">
                            <i class="fas fa-globe {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ app()->getLocale() == 'ar' ? __('messages.arabic') : __('messages.english') }}
                        </span>
                    </button>
                    <ul class="dropdown-menu w-100 dropdown-menu-dark" aria-labelledby="languageDropdown">
                        <li>
                            <a class="dropdown-item language-switcher {{ app()->getLocale() == 'en' ? 'active' : '' }}"
                                href="{{ url()->current() }}?lang=en">
                                <i class="fas fa-flag-usa {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                {{ __('messages.english') }}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item language-switcher {{ app()->getLocale() == 'ar' ? 'active' : '' }}"
                                href="{{ url()->current() }}?lang=ar">
                                <i class="fas fa-flag {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                {{ __('messages.arabic') }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- User Menu -->
            <div class="dropdown">
                <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle"
                   id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="d-flex align-items-center w-100">
                        <i class="fas fa-user-circle fs-4 {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        <div class="flex-grow-1 text-start">
                            <div class="fw-bold small">{{ Auth::user()->name }}</div>
                            <div class="text-white-50 small">{{ __('messages.admin') }}</div>
                        </div>
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-dark shadow w-100" aria-labelledby="dropdownUser1">
                    <li>
                        <a class="dropdown-item" href="{{ route('profile.show') }}">
                            <i class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.profile') }}
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="POST" action="{{ route('logout') }}" class="d-inline w-100">
                            @csrf
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="fas fa-sign-out-alt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                {{ __('messages.logout') }}
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>
