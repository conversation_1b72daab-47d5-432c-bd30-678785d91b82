@extends('layouts.admin')

@section('title', __('messages.create_role'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-plus-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.create_role') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_roles') }}
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            <strong>{{ __('messages.validation_errors') }}</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

            <form action="{{ route('admin.roles.store') }}" method="POST" id="createRoleForm">
                @csrf
                
                <div class="row">
                    <!-- Role Information -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>{{ __('messages.role_information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        {{ __('messages.role_name') }} <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">{{ __('messages.role_name_help') }}</div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">{{ __('messages.description') }}</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">{{ __('messages.role_description_help') }}</div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>{{ __('messages.create_role') }}
                                    </button>
                                    <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                                        {{ __('messages.cancel') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-key me-2"></i>{{ __('messages.assign_permissions') }}
                                </h5>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="selectAllPermissions()">
                                        {{ __('messages.select_all') }}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="deselectAllPermissions()">
                                        {{ __('messages.deselect_all') }}
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                @if($permissions->count() > 0)
                                    <div class="row">
                                        @foreach($permissions as $category => $categoryPermissions)
                                            <div class="col-md-6 mb-4">
                                                <div class="card border-light">
                                                    <div class="card-header bg-light">
                                                        <div class="form-check">
                                                            <input class="form-check-input category-checkbox" 
                                                                   type="checkbox" 
                                                                   id="category_{{ $category }}"
                                                                   onchange="toggleCategoryPermissions('{{ $category }}')">
                                                            <label class="form-check-label fw-bold" for="category_{{ $category }}">
                                                                {{ $permissionCategories[$category] ?? ucfirst(str_replace('_', ' ', $category)) }}
                                                                <span class="badge bg-secondary ms-2">{{ $categoryPermissions->count() }}</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="card-body">
                                                        @foreach($categoryPermissions as $permission)
                                                            <div class="form-check mb-2">
                                                                <input class="form-check-input permission-checkbox permission-{{ $category }}" 
                                                                       type="checkbox" 
                                                                       name="permissions[]" 
                                                                       value="{{ $permission->id }}" 
                                                                       id="permission_{{ $permission->id }}"
                                                                       {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}
                                                                       onchange="updateCategoryCheckbox('{{ $category }}')">
                                                                <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                    {{ $permission->name }}
                                                                    @if($permission->description)
                                                                        <small class="text-muted d-block">{{ $permission->description }}</small>
                                                                    @endif
                                                                </label>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="fas fa-key fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">{{ __('messages.no_permissions_available') }}</h5>
                                        <p class="text-muted">{{ __('messages.no_permissions_description') }}</p>
                                    </div>
                                @endif

                                @error('permissions')
                                    <div class="alert alert-danger mt-3">
                                        <i class="fas fa-exclamation-circle me-2"></i>{{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                    </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    document.querySelectorAll('.category-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.querySelectorAll('.category-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

function toggleCategoryPermissions(category) {
    const categoryCheckbox = document.getElementById(`category_${category}`);
    const permissionCheckboxes = document.querySelectorAll(`.permission-${category}`);
    
    permissionCheckboxes.forEach(checkbox => {
        checkbox.checked = categoryCheckbox.checked;
    });
}

function updateCategoryCheckbox(category) {
    const categoryCheckbox = document.getElementById(`category_${category}`);
    const permissionCheckboxes = document.querySelectorAll(`.permission-${category}`);
    const checkedPermissions = document.querySelectorAll(`.permission-${category}:checked`);
    
    if (checkedPermissions.length === 0) {
        categoryCheckbox.checked = false;
        categoryCheckbox.indeterminate = false;
    } else if (checkedPermissions.length === permissionCheckboxes.length) {
        categoryCheckbox.checked = true;
        categoryCheckbox.indeterminate = false;
    } else {
        categoryCheckbox.checked = false;
        categoryCheckbox.indeterminate = true;
    }
}

// Initialize category checkboxes on page load
document.addEventListener('DOMContentLoaded', function() {
    @foreach($permissions as $category => $categoryPermissions)
        updateCategoryCheckbox('{{ $category }}');
    @endforeach
});

// Form validation
document.getElementById('createRoleForm').addEventListener('submit', function(e) {
    const roleName = document.getElementById('name').value.trim();
    const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
    
    if (!roleName) {
        e.preventDefault();
        alert('{{ __('messages.role_name_required') }}');
        return;
    }
    
    if (checkedPermissions.length === 0) {
        e.preventDefault();
        if (confirm('{{ __('messages.no_permissions_selected_confirm') }}')) {
            // Allow submission without permissions if confirmed
            return;
        }
    }
});
</script>
@endpush
