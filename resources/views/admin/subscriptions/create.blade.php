@extends('layouts.admin')

@section('title', __('messages.add_new_subscription'))

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.add_new_subscription') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_subscriptions') }}
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.please_fix_errors') }}</h6>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form method="POST" action="{{ route('admin.subscriptions.store') }}">
        @csrf

        <div class="row">
            <!-- Subscription Information -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-list {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.subscription_info') }}
                        </h5>
                    </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="client_id" class="form-label">{{ __('messages.client') ?? 'Client' }} <span class="text-danger">*</span></label>
                                            <select class="form-select @error('client_id') is-invalid @enderror" 
                                                    id="client_id" name="client_id" required>
                                                <option value="">{{ __('messages.select_client') ?? 'Select Client' }}</option>
                                                @foreach($clients as $client)
                                                    <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                                        {{ $client->name }} - {{ $client->phone }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('client_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="route_id" class="form-label">{{ __('messages.route') ?? 'Route' }} <span class="text-danger">*</span></label>
                                            <select class="form-select @error('route_id') is-invalid @enderror" 
                                                    id="route_id" name="route_id" required>
                                                <option value="">{{ __('messages.select_route') ?? 'Select Route' }}</option>
                                                @foreach($routes as $route)
                                                    <option value="{{ $route->id }}" 
                                                            data-price="{{ $route->monthly_price }}"
                                                            {{ old('route_id') == $route->id ? 'selected' : '' }}>
                                                        {{ $route->name }} - {{ $route->start_point }} to {{ $route->end_point }}
                                                        ({{ $route->current_students }}/{{ $route->max_students }} students)
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('route_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="student_name" class="form-label">{{ __('messages.student_name') ?? 'Student Name' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('student_name') is-invalid @enderror" 
                                                   id="student_name" name="student_name" value="{{ old('student_name') }}" required>
                                            @error('student_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="subscription_type" class="form-label">{{ __('messages.subscription_type') ?? 'Subscription Type' }} <span class="text-danger">*</span></label>
                                            <select class="form-select @error('subscription_type') is-invalid @enderror" 
                                                    id="subscription_type" name="subscription_type" required>
                                                <option value="">{{ __('messages.select_type') ?? 'Select Type' }}</option>
                                                <option value="monthly" {{ old('subscription_type') == 'monthly' ? 'selected' : '' }}>
                                                    {{ __('messages.monthly') ?? 'Monthly' }}
                                                </option>
                                                <option value="term" {{ old('subscription_type') == 'term' ? 'selected' : '' }}>
                                                    {{ __('messages.term') ?? 'Term' }}
                                                </option>
                                                <option value="yearly" {{ old('subscription_type') == 'yearly' ? 'selected' : '' }}>
                                                    {{ __('messages.yearly') ?? 'Yearly' }}
                                                </option>
                                            </select>
                                            @error('subscription_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="price" class="form-label">{{ __('messages.price') ?? 'Price' }} <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" step="0.01" min="0" 
                                                       class="form-control @error('price') is-invalid @enderror" 
                                                       id="price" name="price" value="{{ old('price') }}" required>
                                                <span class="input-group-text">{{ __('messages.currency') ?? 'SAR' }}</span>
                                            </div>
                                            @error('price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">{{ __('messages.start_date') ?? 'Start Date' }} <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                                   id="start_date" name="start_date" value="{{ old('start_date') }}" required>
                                            @error('start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">{{ __('messages.end_date') ?? 'End Date' }} <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                                   id="end_date" name="end_date" value="{{ old('end_date') }}" required>
                                            @error('end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Info -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>{{ __('messages.subscription_details') ?? 'Subscription Details' }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-2"></i>{{ __('messages.tips') ?? 'Tips' }}</h6>
                                    <ul class="mb-0 small">
                                        <li>{{ __('messages.select_client_first') ?? 'Select the client first' }}</li>
                                        <li>{{ __('messages.route_price_auto_fill') ?? 'Route selection will auto-fill the price' }}</li>
                                        <li>{{ __('messages.end_date_after_start') ?? 'End date must be after start date' }}</li>
                                        <li>{{ __('messages.payment_record_created') ?? 'A payment record will be automatically created' }}</li>
                                    </ul>
                                </div>

                                <div id="route-info" style="display: none;">
                                    <h6>{{ __('messages.selected_route_info') ?? 'Selected Route Information' }}</h6>
                                    <div class="small text-muted">
                                        <div id="route-details"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-main btn-lg w-100">
                                    <i class="fas fa-save me-2"></i>{{ __('messages.create_subscription') ?? 'Create Subscription' }}
                                </button>
                                <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary mt-2 w-100">
                                    <i class="fas fa-times me-1"></i>{{ __('messages.cancel') ?? 'Cancel' }}
                                </a>
                            </div>
                        </div>
                    </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const routeSelect = document.getElementById('route_id');
    const priceInput = document.getElementById('price');
    const subscriptionTypeSelect = document.getElementById('subscription_type');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const routeInfo = document.getElementById('route-info');
    const routeDetails = document.getElementById('route-details');

    // Auto-fill price based on route selection
    routeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const routePrice = selectedOption.dataset.price;
            if (routePrice) {
                priceInput.value = routePrice;
            }
            
            // Show route info
            routeInfo.style.display = 'block';
            routeDetails.innerHTML = selectedOption.textContent;
        } else {
            priceInput.value = '';
            routeInfo.style.display = 'none';
        }
    });

    // Auto-calculate end date based on subscription type and start date
    function calculateEndDate() {
        const startDate = startDateInput.value;
        const subscriptionType = subscriptionTypeSelect.value;
        
        if (startDate && subscriptionType) {
            const start = new Date(startDate);
            let end = new Date(start);
            
            switch (subscriptionType) {
                case 'monthly':
                    end.setMonth(end.getMonth() + 1);
                    break;
                case 'term':
                    end.setMonth(end.getMonth() + 4); // 4 months for a term
                    break;
                case 'yearly':
                    end.setFullYear(end.getFullYear() + 1);
                    break;
            }
            
            // Format date for input
            const formattedDate = end.toISOString().split('T')[0];
            endDateInput.value = formattedDate;
        }
    }

    subscriptionTypeSelect.addEventListener('change', calculateEndDate);
    startDateInput.addEventListener('change', calculateEndDate);

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.setAttribute('min', today);
    
    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.setAttribute('min', this.value);
    });
});
</script>
@endpush
@endsection
