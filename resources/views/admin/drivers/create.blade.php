@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        @include('admin.partials.sidebar')

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-plus me-2"></i>Add New Driver
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.drivers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Drivers
                    </a>
                </div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.drivers.store') }}">
                @csrf
                
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required
                                           placeholder="Enter driver's full name">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone') }}" required
                                                   placeholder="+966 50 123 4567">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email') }}"
                                                   placeholder="<EMAIL>">
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="national_id" class="form-label">National ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                                           id="national_id" name="national_id" value="{{ old('national_id') }}" required
                                           placeholder="1234567890">
                                    @error('national_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror" 
                                              id="address" name="address" rows="3" 
                                              placeholder="Enter driver's address">{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-phone me-2"></i>Emergency Contact
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="emergency_contact_name" class="form-label">Contact Name</label>
                                    <input type="text" class="form-control @error('emergency_contact_name') is-invalid @enderror" 
                                           id="emergency_contact_name" name="emergency_contact_name" 
                                           value="{{ old('emergency_contact_name') }}"
                                           placeholder="Emergency contact full name">
                                    @error('emergency_contact_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact_phone" class="form-label">Contact Phone</label>
                                            <input type="tel" class="form-control @error('emergency_contact_phone') is-invalid @enderror" 
                                                   id="emergency_contact_phone" name="emergency_contact_phone" 
                                                   value="{{ old('emergency_contact_phone') }}"
                                                   placeholder="+966 50 123 4567">
                                            @error('emergency_contact_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact_relation" class="form-label">Relationship</label>
                                            <input type="text" class="form-control @error('emergency_contact_relation') is-invalid @enderror" 
                                                   id="emergency_contact_relation" name="emergency_contact_relation" 
                                                   value="{{ old('emergency_contact_relation') }}"
                                                   placeholder="e.g., Spouse, Brother, etc.">
                                            @error('emergency_contact_relation')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- License & Employment -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-id-card me-2"></i>License & Employment
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="license_number" class="form-label">License Number <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('license_number') is-invalid @enderror" 
                                                   id="license_number" name="license_number" value="{{ old('license_number') }}" required
                                                   placeholder="License number">
                                            @error('license_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="license_expiry" class="form-label">License Expiry <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('license_expiry') is-invalid @enderror" 
                                                   id="license_expiry" name="license_expiry" value="{{ old('license_expiry') }}" required>
                                            @error('license_expiry')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hire_date" class="form-label">Hire Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('hire_date') is-invalid @enderror" 
                                                   id="hire_date" name="hire_date" value="{{ old('hire_date', date('Y-m-d')) }}" required>
                                            @error('hire_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="salary" class="form-label">Monthly Salary (SAR)</label>
                                            <div class="input-group">
                                                <span class="input-group-text">SAR</span>
                                                <input type="number" class="form-control @error('salary') is-invalid @enderror" 
                                                       id="salary" name="salary" value="{{ old('salary') }}" 
                                                       min="0" step="0.01" placeholder="3000.00">
                                                @error('salary')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>Suspended</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-car me-2"></i>Vehicle Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_type" class="form-label">Vehicle Type</label>
                                            <select class="form-select @error('vehicle_type') is-invalid @enderror" 
                                                    id="vehicle_type" name="vehicle_type">
                                                <option value="">Select Type</option>
                                                <option value="bus" {{ old('vehicle_type') == 'bus' ? 'selected' : '' }}>Bus</option>
                                                <option value="van" {{ old('vehicle_type') == 'van' ? 'selected' : '' }}>Van</option>
                                                <option value="minibus" {{ old('vehicle_type') == 'minibus' ? 'selected' : '' }}>Minibus</option>
                                            </select>
                                            @error('vehicle_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_capacity" class="form-label">Vehicle Capacity</label>
                                            <input type="number" class="form-control @error('vehicle_capacity') is-invalid @enderror" 
                                                   id="vehicle_capacity" name="vehicle_capacity" value="{{ old('vehicle_capacity') }}" 
                                                   min="1" placeholder="30">
                                            @error('vehicle_capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_model" class="form-label">Vehicle Model</label>
                                            <input type="text" class="form-control @error('vehicle_model') is-invalid @enderror" 
                                                   id="vehicle_model" name="vehicle_model" value="{{ old('vehicle_model') }}"
                                                   placeholder="e.g., Toyota Hiace">
                                            @error('vehicle_model')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_year" class="form-label">Vehicle Year</label>
                                            <input type="text" class="form-control @error('vehicle_year') is-invalid @enderror" 
                                                   id="vehicle_year" name="vehicle_year" value="{{ old('vehicle_year') }}"
                                                   placeholder="2023" maxlength="4">
                                            @error('vehicle_year')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_plate_number" class="form-label">Plate Number</label>
                                            <input type="text" class="form-control @error('vehicle_plate_number') is-invalid @enderror" 
                                                   id="vehicle_plate_number" name="vehicle_plate_number" 
                                                   value="{{ old('vehicle_plate_number') }}"
                                                   placeholder="ABC-1234">
                                            @error('vehicle_plate_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="vehicle_color" class="form-label">Vehicle Color</label>
                                            <input type="text" class="form-control @error('vehicle_color') is-invalid @enderror" 
                                                   id="vehicle_color" name="vehicle_color" value="{{ old('vehicle_color') }}"
                                                   placeholder="White">
                                            @error('vehicle_color')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="vehicle_notes" class="form-label">Vehicle Notes</label>
                                    <textarea class="form-control @error('vehicle_notes') is-invalid @enderror" 
                                              id="vehicle_notes" name="vehicle_notes" rows="2" 
                                              placeholder="Any additional notes about the vehicle">{{ old('vehicle_notes') }}</textarea>
                                    @error('vehicle_notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sticky-note me-2"></i>Additional Notes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" 
                                      placeholder="Any additional notes about the driver">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ route('admin.drivers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-main">
                        <i class="fas fa-save me-1"></i>Create Driver
                    </button>
                </div>
            </form>
        </main>
    </div>
</div>
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
</style>
@endpush
