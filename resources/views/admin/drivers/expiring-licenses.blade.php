@extends('layouts.admin')

@section('title', __('messages.expiring_licenses'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.expiring_licenses') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.drivers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_drivers') }}
            </a>
        </div>
    </div>

    <!-- Alert if no expiring licenses -->
    @if($drivers->isEmpty())
        <div class="alert alert-success">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ __('messages.no_expiring_licenses') }}
        </div>
    @else
        <!-- Warning Alert -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ __('messages.drivers_with_expiring_licenses', ['count' => $drivers->count()]) }}
        </div>
    @endif

    <!-- Drivers List -->
    @if($drivers->isNotEmpty())
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.drivers_with_expiring_licenses') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('messages.driver_name') }}</th>
                                <th>{{ __('messages.phone_number') }}</th>
                                <th>{{ __('messages.license_number') }}</th>
                                <th>{{ __('messages.license_expiry') }}</th>
                                <th>{{ __('messages.days_remaining') }}</th>
                                <th>{{ __('messages.current_route') }}</th>
                                <th>{{ __('messages.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($drivers as $driver)
                                @php
                                    $daysRemaining = now()->diffInDays($driver->license_expiry, false);
                                    $urgencyClass = $daysRemaining <= 7 ? 'table-danger' : ($daysRemaining <= 15 ? 'table-warning' : '');
                                    $currentRoute = $driver->currentRoute();
                                @endphp
                                <tr class="{{ $urgencyClass }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm {{ app()->getLocale() == 'ar' ? 'ms-3' : 'me-3' }}">
                                                <div class="avatar-title rounded-circle bg-primary">
                                                    {{ substr($driver->name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $driver->name }}</h6>
                                                <small class="text-muted">{{ __('messages.id') }}: {{ $driver->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $driver->phone }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $driver->license_number }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $daysRemaining <= 0 ? 'bg-danger' : ($daysRemaining <= 7 ? 'bg-warning' : 'bg-info') }}">
                                            {{ $driver->license_expiry->format('Y-m-d') }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($daysRemaining <= 0)
                                            <span class="badge bg-danger">{{ __('messages.expired') }}</span>
                                        @elseif($daysRemaining <= 7)
                                            <span class="badge bg-warning">{{ $daysRemaining }} {{ __('messages.days') }}</span>
                                        @else
                                            <span class="badge bg-info">{{ $daysRemaining }} {{ __('messages.days') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($currentRoute)
                                            <a href="{{ route('admin.routes.show', $currentRoute) }}" class="text-decoration-none">
                                                {{ $currentRoute->name }}
                                            </a>
                                        @else
                                            <span class="text-muted">{{ __('messages.no_route_assigned') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @can('update admin_drivers')
                                                <a href="{{ route('admin.drivers.edit', $driver) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="{{ __('messages.edit_driver') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @can('view admin_drivers')
                                                <a href="{{ route('admin.drivers.show', $driver) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="{{ __('messages.view_driver') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif
</div>

@push('styles')
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }
    
    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        color: white;
    }
    
    .table-danger {
        --bs-table-accent-bg: rgba(220, 53, 69, 0.1);
    }
    
    .table-warning {
        --bs-table-accent-bg: rgba(255, 193, 7, 0.1);
    }
</style>
@endpush
@endsection
