@extends('layouts.admin')

@section('title', __('messages.add_new_client'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-plus {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.add_new_client') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.clients.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_clients') }}
            </a>
        </div>
    </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.clients.store') }}">
                @csrf
                
                <p class="text-danger text-center mb-4">
                    <strong>*</strong> {{ __('messages.fields_marked_required') }}
                </p>

                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.basic_information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">{{ __('messages.full_name') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">{{ __('messages.email_address') }} <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">{{ __('messages.phone_number') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone') }}" required>
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">{{ __('messages.password') }} <span class="text-danger">*</span></label>
                                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                                   id="password" name="password" required>
                                            @error('password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-phone {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.contact_information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="home_phone" class="form-label">{{ __('messages.home_phone') }}</label>
                                            <input type="text" class="form-control @error('home_phone') is-invalid @enderror" 
                                                   id="home_phone" name="home_phone" value="{{ old('home_phone') }}">
                                            @error('home_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="mother_phone" class="form-label">{{ __('messages.mother_phone') }}</label>
                                            <input type="text" class="form-control @error('mother_phone') is-invalid @enderror" 
                                                   id="mother_phone" name="mother_phone" value="{{ old('mother_phone') }}">
                                            @error('mother_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="father_phone" class="form-label">{{ __('messages.father_phone') }}</label>
                                            <input type="text" class="form-control @error('father_phone') is-invalid @enderror" 
                                                   id="father_phone" name="father_phone" value="{{ old('father_phone') }}">
                                            @error('father_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="extra_phone" class="form-label">{{ __('messages.extra_phone') }}</label>
                                            <input type="text" class="form-control @error('extra_phone') is-invalid @enderror" 
                                                   id="extra_phone" name="extra_phone" value="{{ old('extra_phone') }}">
                                            @error('extra_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_priority" class="form-label">{{ __('messages.contact_priority') }}</label>
                                    <select class="form-select @error('contact_priority') is-invalid @enderror"
                                            id="contact_priority" name="contact_priority">
                                        <option value="">{{ __('messages.select_option') }}</option>
                                        <option value="father" {{ old('contact_priority') == 'father' ? 'selected' : '' }}>{{ __('messages.contact_father') }}</option>
                                        <option value="mother" {{ old('contact_priority') == 'mother' ? 'selected' : '' }}>{{ __('messages.contact_mother') }}</option>
                                    </select>
                                    @error('contact_priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.additional_notes') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="father_job" class="form-label">{{ __('messages.father_job') }}</label>
                                    <input type="text" class="form-control @error('father_job') is-invalid @enderror" 
                                           id="father_job" name="father_job" value="{{ old('father_job') }}">
                                    @error('father_job')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="mother_job" class="form-label">{{ __('messages.mother_job') }}</label>
                                    <input type="text" class="form-control @error('mother_job') is-invalid @enderror" 
                                           id="mother_job" name="mother_job" value="{{ old('mother_job') }}">
                                    @error('mother_job')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="area" class="form-label">{{ __('messages.area') }}</label>
                                    <input type="text" class="form-control @error('area') is-invalid @enderror" 
                                           id="area" name="area" value="{{ old('area') }}">
                                    @error('area')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="location" class="form-label">{{ __('messages.location') }}</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                           id="location" name="location" value="{{ old('location') }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="car_type" class="form-label">{{ __('messages.car_type') }}</label>
                                    <input type="text" class="form-control @error('car_type') is-invalid @enderror" 
                                           id="car_type" name="car_type" value="{{ old('car_type') }}">
                                    @error('car_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="study_start_date" class="form-label">{{ __('messages.study_start_date') }}</label>
                                    <input type="date" class="form-control @error('study_start_date') is-invalid @enderror" 
                                           id="study_start_date" name="study_start_date" value="{{ old('study_start_date') }}">
                                    @error('study_start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subscription_type" class="form-label">{{ __('messages.subscription_type') }}</label>
                                    <select class="form-select @error('subscription_type') is-invalid @enderror"
                                            id="subscription_type" name="subscription_type">
                                        <option value="">{{ __('messages.select_option') }}</option>
                                        <option value="one_time" {{ old('subscription_type') == 'one_time' ? 'selected' : '' }}>{{ __('messages.subscription_one_time') }}</option>
                                        <option value="monthly" {{ old('subscription_type') == 'monthly' ? 'selected' : '' }}>{{ __('messages.subscription_monthly') }}</option>
                                    </select>
                                    @error('subscription_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="client_type" class="form-label">{{ __('messages.client_type') }}</label>
                                    <select class="form-select @error('client_type') is-invalid @enderror"
                                            id="client_type" name="client_type">
                                        <option value="">{{ __('messages.select_option') }}</option>
                                        <option value="new" {{ old('client_type') == 'new' ? 'selected' : '' }}>{{ __('messages.new_client') }}</option>
                                        <option value="old" {{ old('client_type') == 'old' ? 'selected' : '' }}>{{ __('messages.old_client') }}</option>
                                    </select>
                                    @error('client_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address and Comments -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marker-alt {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.address') }} & {{ __('messages.comments') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="address" class="form-label">{{ __('messages.address') }}</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="comments" class="form-label">{{ __('messages.comments') }}</label>
                            <textarea class="form-control @error('comments') is-invalid @enderror" 
                                      id="comments" name="comments" rows="3" 
                                      placeholder="Any additional notes or comments about the client">{{ old('comments') }}</textarea>
                            @error('comments')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ route('admin.clients.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                    </a>
                    <button type="submit" class="btn btn-main">
                        <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.add_client') }}
                    </button>
        </div>
    </form>
</div>
@endsection
