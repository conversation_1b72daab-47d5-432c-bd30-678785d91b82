@extends('layouts.admin')

@section('title', __('messages.edit_client'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-edit {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.edit_client') }}: {{ $client->user->name }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.clients.index') }}" class="btn btn-outline-secondary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back_to_clients') }}
            </a>
            <a href="{{ route('admin.clients.show', $client) }}" class="btn btn-outline-info">
                <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.view_client') }}
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.form_has_errors') }}</h6>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

            <form method="POST" action="{{ route('admin.clients.update', $client) }}">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.basic_information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">{{ __('messages.full_name') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name', $client->user->name) }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">{{ __('messages.email') }} <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email', $client->user->email) }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">{{ __('messages.phone_number') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone', $client->user->phone) }}" required>
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">{{ __('messages.password') }} <small class="text-muted">({{ __('messages.leave_blank_to_keep_current') }})</small></label>
                                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                                   id="password" name="password">
                                            @error('password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-phone me-2"></i>Contact Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="home_phone" class="form-label">Home Phone</label>
                                            <input type="text" class="form-control @error('home_phone') is-invalid @enderror" 
                                                   id="home_phone" name="home_phone" value="{{ old('home_phone', $client->home_phone) }}">
                                            @error('home_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="mother_phone" class="form-label">Mother's Phone</label>
                                            <input type="text" class="form-control @error('mother_phone') is-invalid @enderror" 
                                                   id="mother_phone" name="mother_phone" value="{{ old('mother_phone', $client->mother_phone) }}">
                                            @error('mother_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="father_phone" class="form-label">Father's Phone</label>
                                            <input type="text" class="form-control @error('father_phone') is-invalid @enderror" 
                                                   id="father_phone" name="father_phone" value="{{ old('father_phone', $client->father_phone) }}">
                                            @error('father_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="extra_phone" class="form-label">Extra Phone</label>
                                            <input type="text" class="form-control @error('extra_phone') is-invalid @enderror" 
                                                   id="extra_phone" name="extra_phone" value="{{ old('extra_phone', $client->extra_phone) }}">
                                            @error('extra_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_priority" class="form-label">Contact Priority</label>
                                    <input type="text" class="form-control @error('contact_priority') is-invalid @enderror" 
                                           id="contact_priority" name="contact_priority" value="{{ old('contact_priority', $client->contact_priority) }}"
                                           placeholder="e.g., Call mother first, then father">
                                    @error('contact_priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Additional Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="father_job" class="form-label">Father's Job</label>
                                    <input type="text" class="form-control @error('father_job') is-invalid @enderror" 
                                           id="father_job" name="father_job" value="{{ old('father_job', $client->father_job) }}">
                                    @error('father_job')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="mother_job" class="form-label">Mother's Job</label>
                                    <input type="text" class="form-control @error('mother_job') is-invalid @enderror" 
                                           id="mother_job" name="mother_job" value="{{ old('mother_job', $client->mother_job) }}">
                                    @error('mother_job')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="area" class="form-label">Area</label>
                                    <input type="text" class="form-control @error('area') is-invalid @enderror" 
                                           id="area" name="area" value="{{ old('area', $client->area) }}">
                                    @error('area')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                           id="location" name="location" value="{{ old('location', $client->location) }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="car_type" class="form-label">Car Type</label>
                                    <input type="text" class="form-control @error('car_type') is-invalid @enderror" 
                                           id="car_type" name="car_type" value="{{ old('car_type', $client->car_type) }}">
                                    @error('car_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="study_start_date" class="form-label">Study Start Date</label>
                                    <input type="date" class="form-control @error('study_start_date') is-invalid @enderror" 
                                           id="study_start_date" name="study_start_date" 
                                           value="{{ old('study_start_date', $client->study_start_date ? $client->study_start_date->format('Y-m-d') : '') }}">
                                    @error('study_start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subscription_type" class="form-label">Subscription Type</label>
                                    <select class="form-select @error('subscription_type') is-invalid @enderror" 
                                            id="subscription_type" name="subscription_type">
                                        <option value="">Select Type</option>
                                        <option value="monthly" {{ old('subscription_type', $client->subscription_type) == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        <option value="semester" {{ old('subscription_type', $client->subscription_type) == 'semester' ? 'selected' : '' }}>Semester</option>
                                        <option value="yearly" {{ old('subscription_type', $client->subscription_type) == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                    </select>
                                    @error('subscription_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="client_type" class="form-label">Client Type</label>
                                    <select class="form-select @error('client_type') is-invalid @enderror" 
                                            id="client_type" name="client_type">
                                        <option value="">Select Type</option>
                                        <option value="regular" {{ old('client_type', $client->client_type) == 'regular' ? 'selected' : '' }}>Regular</option>
                                        <option value="vip" {{ old('client_type', $client->client_type) == 'vip' ? 'selected' : '' }}>VIP</option>
                                        <option value="corporate" {{ old('client_type', $client->client_type) == 'corporate' ? 'selected' : '' }}>Corporate</option>
                                    </select>
                                    @error('client_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address and Comments -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>Address & Comments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="address" class="form-label">Full Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address', $client->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="comments" class="form-label">Comments</label>
                            <textarea class="form-control @error('comments') is-invalid @enderror" 
                                      id="comments" name="comments" rows="3" 
                                      placeholder="Any additional notes or comments about the client">{{ old('comments', $client->comments) }}</textarea>
                            @error('comments')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ route('admin.clients.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                    </a>
                    <button type="submit" class="btn btn-main">
                        <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.update_client') }}
                    </button>
                </div>
            </form>
</div>
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
</style>
@endpush
