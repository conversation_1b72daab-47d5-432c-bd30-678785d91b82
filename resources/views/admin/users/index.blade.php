@extends('layouts.admin')

@section('title', __('messages.user_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ __('messages.user_management') }}</h1>
        @can('create users')
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                {{ __('messages.add_user') }}
            </a>
        @endcan
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.total_users') }}</h6>
                            <h3 class="mb-0">{{ $stats['total_users'] ?? 0 }}</h3>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.active_users') }}</h6>
                            <h3 class="mb-0">{{ $stats['active_users'] ?? 0 }}</h3>
                        </div>
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.admin_users') }}</h6>
                            <h3 class="mb-0">{{ $stats['admin_users'] ?? 0 }}</h3>
                        </div>
                        <i class="fas fa-user-shield fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">{{ __('messages.new_users_this_month') }}</h6>
                            <h3 class="mb-0">{{ $stats['new_users_this_month'] ?? 0 }}</h3>
                        </div>
                        <i class="fas fa-user-plus fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.users') }}</h6>
            <div class="d-flex">
                @can('create users')
                    <button class="btn btn-sm btn-outline-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                        <i class="fas fa-tasks {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                        {{ __('messages.bulk_actions') }}
                    </button>
                @endcan
                <button class="btn btn-sm btn-outline-secondary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                    {{ __('messages.refresh') }}
                </button>
            </div>
        </div>
        <div class="card-body">
            {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
        </div>
    </div>
</div>

<!-- Bulk Action Modal -->
@can('update users')
<div class="modal fade" id="bulkActionModal" tabindex="-1" aria-labelledby="bulkActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionModalLabel">{{ __('messages.bulk_actions') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ __('messages.close') }}"></button>
            </div>
            <form id="bulkActionForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulk_action" class="form-label">{{ __('messages.select_action') }}</label>
                        <select class="form-select" id="bulk_action" name="action" required>
                            <option value="">{{ __('messages.choose_action') }}</option>
                            <option value="activate">{{ __('messages.activate_users') }}</option>
                            <option value="deactivate">{{ __('messages.deactivate_users') }}</option>
                            <option value="assign_role">{{ __('messages.assign_role') }}</option>
                            <option value="remove_role">{{ __('messages.remove_role') }}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 d-none" id="role_selection">
                        <label for="role_id" class="form-label">{{ __('messages.select_role') }}</label>
                        <select class="form-select" id="role_id" name="role_id">
                            <option value="">{{ __('messages.choose_role') }}</option>
                            @foreach($roles ?? [] as $role)
                                <option value="{{ $role->id }}">{{ $role->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        {{ __('messages.bulk_action_warning') }}
                    </div>
                    
                    <div id="selected_users_count" class="text-muted">
                        {{ __('messages.no_users_selected') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('messages.cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="executeBulkAction" disabled>
                        {{ __('messages.execute_action') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
$(document).ready(function() {
    // Handle bulk action selection
    $('#bulk_action').on('change', function() {
        const action = $(this).val();
        if (action === 'assign_role' || action === 'remove_role') {
            $('#role_selection').removeClass('d-none');
            $('#role_id').prop('required', true);
        } else {
            $('#role_selection').addClass('d-none');
            $('#role_id').prop('required', false);
        }
    });
    
    // Handle user selection for bulk actions
    $(document).on('change', '.user-checkbox', function() {
        updateBulkActionButton();
    });
    
    // Handle select all checkbox
    $(document).on('change', '#select-all-users', function() {
        $('.user-checkbox').prop('checked', $(this).is(':checked'));
        updateBulkActionButton();
    });
    
    function updateBulkActionButton() {
        const selectedCount = $('.user-checkbox:checked').length;
        $('#selected_users_count').text(
            selectedCount > 0 
                ? `{{ __('messages.users_selected') }}: ${selectedCount}`
                : '{{ __('messages.no_users_selected') }}'
        );
        $('#executeBulkAction').prop('disabled', selectedCount === 0);
    }
    
    // Handle bulk action form submission
    $('#bulkActionForm').on('submit', function(e) {
        e.preventDefault();
        
        const selectedUsers = [];
        $('.user-checkbox:checked').each(function() {
            selectedUsers.push($(this).val());
        });
        
        if (selectedUsers.length === 0) {
            alert('{{ __("messages.please_select_users") }}');
            return;
        }
        
        // Add selected user IDs to form
        selectedUsers.forEach(function(userId) {
            $('<input>').attr({
                type: 'hidden',
                name: 'user_ids[]',
                value: userId
            }).appendTo('#bulkActionForm');
        });
        
        // Set form action
        $(this).attr('action', '{{ route("admin.users.bulk-action") }}');
        
        // Submit form
        this.submit();
    });
    
    // Handle individual user status toggle
    $(document).on('click', '.toggle-user-status', function(e) {
        e.preventDefault();
        
        const userId = $(this).data('user-id');
        const currentStatus = $(this).data('current-status');
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        
        if (confirm(`{{ __('messages.confirm_user_status_change') }}`)) {
            $.ajax({
                url: `/admin/users/${userId}/toggle-status`,
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    status: newStatus
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '{{ __("messages.error_occurred") }}');
                    }
                },
                error: function() {
                    alert('{{ __("messages.error_occurred") }}');
                }
            });
        }
    });
});
</script>
@endpush
