<?php

return [
    'client_form'                => 'Client Registration Form',
    'client_list'                => 'Client List',
    'call_center'                => 'Customer Service Center',
    'form_has_errors'            => 'There are errors in your submission!',
    'please_check_fields'        => 'Please check the highlighted fields and correct them.',
    'fields_marked_required'     => 'Fields marked with * are required',
    'student_name'               => 'Student Name',
    'parent_information'         => 'Parent Information',
    'father_job'                 => 'Job of Guardian (Father)',
    'mother_job'                 => 'Job of Guardian (Mother)',
    'home_phone'                 => 'Home Phone Number',
    'mother_phone'               => 'Mother\'s Phone Number',
    'father_phone'               => 'Father\'s Phone Number',
    'extra_phone'                => 'Additional Phone Number',
    'contact_priority'           => 'Communication Priority',
    'contact_father'             => 'Father',
    'contact_mother'             => 'Mother',
    'address'                    => 'Detailed Address',
    'location'                   => 'Location',
    'area'                       => 'Area',
    'school'                     => 'School',
    'education_department'       => 'Educational Department',
    'national'                   => 'National',
    'international'              => 'Inter National',
    'igsec'                      => 'IGSEC',
    'french'                     => 'French',
    'american'                   => 'American Diploma',
    'dutch'                      => 'Special Cases',
    'education_stage'            => 'Educational Level',
    'class_level'                => 'Class Level',
    'kg'                         => 'KG',
    'middle'                     => 'Middle',
    'junior'                     => 'Junior',
    'senior'                     => 'Senior',
    'primary'                    => 'Primary',
    'secondary'                  => 'Secondary',
    'preparatory'                => 'Preparatory',
    'entry_time'                 => 'Entry Time',
    'exit_time'                  => 'Check Out Time',
    'car_type'                   => 'Type of Car',
    'qasrawy'                    => 'Qasrawy',
    'high_roof'                  => 'High Roof',
    'private7'                   => 'Private (7 Seats)',
    'private_classic'            => 'Private Car',
    'coaster'                    => 'Coaster (21 Seats)',
    'chevrolet'                  => 'Chevrolet',
    'study_start_date'           => 'Study Start Date',
    'basic_information'          => 'Basic Information',
    'update_client'              => 'Update Client',
    'no_expiring_licenses'       => 'No drivers have licenses expiring within the next 30 days.',
    'drivers_with_expiring_licenses' => 'Drivers with Expiring Licenses',
    'days_remaining'             => 'Days Remaining',
    'days'                       => 'days',
    'no_route_assigned'          => 'No route assigned',
    'subscription_type'          => 'Subscription Type',
    'subscription_one_time'      => 'Annual Subscription (One Payment)',
    'subscription_monthly'       => 'Annual Subscription (Monthly Installments)',
    'client_type'                => 'Client Type',
    'new_client'                 => 'New Client',
    'old_client'                 => 'Previous Client',
    'comments'                   => 'Comments',
    'submit'                     => 'Submit',
    'created_at'                 => 'Created At',
    'form_received_successfully' => 'Form submitted successfully!',
    'back_to_form'               => 'Register New Client',
    'students'                   => 'Students',
    'add_student'                => 'Add Student',
    'remove_student'             => 'Remove Student',
    'student_number'             => 'Student #',
    'multiple_students_info'     => 'You can add multiple students in the same form',
    'user_account_created'       => 'User account created successfully',
    'default_password_info'      => 'A user account has been created for you with a default password. Account details will be sent via phone.',
    'other_school'               => 'Other School',
    'client_registration'        => 'Client Registration',
    'royal_transit'              => 'Royal Transit',
    'english'                    => 'English',
    'arabic'                     => 'العربية',
    'geolocation_not_supported'  => 'Geolocation is not supported in this browser.',
    'location_access_denied'     => 'Could not get location. Please make sure you allowed location access.',

    // Admin Panel Translations
    'admin_panel'                => 'Admin Panel',
    'dashboard'                  => 'Dashboard',
    'clients'                    => 'Clients',
    'routes'                     => 'Routes',
    'drivers'                    => 'Drivers',
    'financial'                  => 'Financial',
    'reports'                    => 'Reports',
    'settings'                   => 'Settings',
    'logout'                     => 'Logout',

    // Client Management
    'client_management'          => 'Client Management',
    'add_client'                 => 'Add Client',
    'edit_client'                => 'Edit Client',
    'view_client'                => 'View Client',
    'delete_client'              => 'Delete Client',
    'client_details'             => 'Client Details',
    'total_clients'              => 'Total Clients',
    'active_clients'             => 'Active Clients',
    'pending_clients'            => 'Pending Clients',

    // Route Management
    'route_management'           => 'Route Management',
    'add_route'                  => 'Add Route',
    'edit_route'                 => 'Edit Route',
    'view_route'                 => 'View Route',
    'delete_route'               => 'Delete Route',
    'route_details'              => 'Route Details',
    'total_routes'               => 'Total Routes',
    'active_routes'              => 'Active Routes',
    'route_capacity'             => 'Route Capacity',
    'from_area'                  => 'From Area',
    'to_school'                  => 'To School',
    'pickup_time'                => 'Pickup Time',
    'dropoff_time'               => 'Dropoff Time',
    'current_students'           => 'Current Students',
    'routes_list'                => 'Routes List',
    'total_capacity'             => 'Total Capacity',
    'capacity_report'            => 'Capacity Report',
    'driver_assignments'         => 'Driver Assignments',
    'schedule_overview'          => 'Schedule Overview',
    'export_routes'              => 'Export Routes',
    'route_capacity_report'      => 'Route Capacity Report',
    'capacity'                   => 'Capacity',
    'route_stops'                => 'Route Stops',
    'add_stop'                   => 'Add Stop',
    'remove_stop'                => 'Remove Stop',
    'stop_name'                  => 'Stop Name',
    'stop_time'                  => 'Stop Time',
    'assigned_driver'            => 'Assigned Driver',
    'no_driver_assigned'         => 'No Driver Assigned',

    // Driver Management
    'driver_management'          => 'Driver Management',
    'add_driver'                 => 'Add Driver',
    'edit_driver'                => 'Edit Driver',
    'view_driver'                => 'View Driver',
    'delete_driver'              => 'Delete Driver',
    'driver_details'             => 'Driver Details',
    'total_drivers'              => 'Total Drivers',
    'active_drivers'             => 'Active Drivers',
    'assigned_drivers'           => 'Assigned to Routes',
    'expiring_licenses'          => 'Expiring Licenses',
    'driver_name'                => 'Driver Name',
    'phone_number'               => 'Phone Number',
    'email_address'              => 'Email Address',
    'national_id'                => 'National ID',
    'license_number'             => 'License Number',
    'license_expiry'             => 'License Expiry',
    'hire_date'                  => 'Hire Date',
    'monthly_salary'             => 'Monthly Salary',
    'driver_status'              => 'Driver Status',
    'active'                     => 'Active',
    'inactive'                   => 'Inactive',
    'suspended'                  => 'Suspended',
    'vehicle_information'        => 'Vehicle Information',
    'vehicle_type'               => 'Vehicle Type',
    'vehicle_capacity'           => 'Vehicle Capacity',
    'vehicle_model'              => 'Vehicle Model',
    'vehicle_year'               => 'Vehicle Year',
    'plate_number'               => 'Plate Number',
    'vehicle_color'              => 'Vehicle Color',
    'vehicle_notes'              => 'Vehicle Notes',
    'emergency_contact'          => 'Emergency Contact',
    'contact_name'               => 'Contact Name',
    'contact_phone'              => 'Contact Phone',
    'relationship'               => 'Relationship',
    'personal_information'       => 'Personal Information',
    'license_employment'         => 'License & Employment',
    'additional_notes'           => 'Additional Notes',
    'current_assignment'         => 'Current Assignment',
    'route_assignment_history'   => 'Route Assignment History',
    'driver_statistics'          => 'Driver Statistics',
    'years_of_service'           => 'Years of Service',
    'license_status'             => 'License Status',
    'valid'                      => 'Valid',
    'expired'                    => 'Expired',
    'expiring_soon'              => 'Expiring Soon',
    'suspend_driver'             => 'Suspend Driver',
    'activate_driver'            => 'Activate Driver',
    'remove_from_route'          => 'Remove from Route',
    'assign_to_route'            => 'Assign to Route',

    // Financial Management
    'financial_management'       => 'Financial Management',
    'total_revenue'              => 'Total Revenue',
    'monthly_revenue'            => 'Monthly Revenue',
    'pending_payments'           => 'Pending Payments',
    'payment_history'            => 'Payment History',
    'subscription_revenue'       => 'Subscription Revenue',
    'payment_status'             => 'Payment Status',
    'paid'                       => 'Paid',
    'pending'                    => 'Pending',
    'overdue'                    => 'Overdue',

    // Common Actions
    'actions'                    => 'Actions',
    'create'                     => 'Create',
    'edit'                       => 'Edit',
    'view'                       => 'View',
    'delete'                     => 'Delete',
    'save'                       => 'Save',
    'cancel'                     => 'Cancel',
    'update'                     => 'Update',
    'back'                       => 'Back',
    'search'                     => 'Search',
    'filter'                     => 'Filter',
    'export'                     => 'Export',
    'import'                     => 'Import',
    'refresh'                    => 'Refresh',
    'loading'                    => 'Loading...',
    'no_data'                    => 'No data available',
    'confirm_delete'             => 'Are you sure you want to delete this item?',
    'success'                    => 'Success',
    'error'                      => 'Error',
    'warning'                    => 'Warning',
    'info'                       => 'Information',

    // Form Validation
    'required_field'             => 'This field is required',
    'invalid_email'              => 'Please enter a valid email address',
    'invalid_phone'              => 'Please enter a valid phone number',
    'invalid_date'               => 'Please enter a valid date',
    'min_length'                 => 'Minimum length is :min characters',
    'max_length'                 => 'Maximum length is :max characters',

    // Status Messages
    'created_successfully'       => 'Created successfully',
    'updated_successfully'       => 'Updated successfully',
    'deleted_successfully'       => 'Deleted successfully',
    'operation_failed'           => 'Operation failed',
    'permission_denied'          => 'Permission denied',
    'not_found'                  => 'Item not found',

    // Dashboard translations
    'dashboard'                  => 'Dashboard',
    'export'                     => 'Export',
    'refresh'                    => 'Refresh',
    'view_all'                   => 'View All',
    'total_clients'              => 'Total Clients',
    'this_month'                 => 'This Month',
    'active_routes'              => 'Active Routes',
    'total'                      => 'Total',
    'active_drivers'             => 'Active Drivers',
    'on_leave'                   => 'On Leave',
    'monthly_revenue'            => 'Monthly Revenue',
    'vs_last_month'              => 'vs Last Month',
    'view_financial'             => 'View Financial',
    'total_students'             => 'Total Students',
    'active'                     => 'Active',
    'active_subscriptions'       => 'Active Subscriptions',
    'pending'                    => 'Pending',
    'pending_payments'           => 'Pending Payments',
    'overdue'                    => 'Overdue',
    'total_revenue'              => 'Total Revenue',
    'all_time'                   => 'All Time',
    'recent_subscriptions'       => 'Recent Subscriptions',
    'client'                     => 'Client',
    'route'                      => 'Route',
    'type'                       => 'Type',
    'status'                     => 'Status',
    'created'                    => 'Created',
    'no_recent_subscriptions'    => 'No recent subscriptions',
    'recent_payments'            => 'Recent Payments',
    'no_recent_payments'         => 'No recent payments',
    'quick_actions'              => 'Quick Actions',
    'add_client'                 => 'Add Client',
    'add_route'                  => 'Add Route',
    'add_driver'                 => 'Add Driver',
    'view_reports'               => 'View Reports',
    'manage_routes'              => 'Manage Routes',
    'manage_drivers'             => 'Manage Drivers',
    'completed'                  => 'Completed',
    'monthly'                    => 'Monthly',
    'yearly'                     => 'Yearly',

    // Audit logging messages
    'admin_client_created'       => 'Admin created new client',
    'admin_client_updated'       => 'Admin updated client information',
    'admin_client_deleted'       => 'Admin deleted client',
    'admin_client_viewed'        => 'Admin viewed client details',
    'admin_route_created'        => 'Admin created new route',
    'admin_route_updated'        => 'Admin updated route information',
    'admin_route_deleted'        => 'Admin deleted route',
    'admin_route_viewed'         => 'Admin viewed route details',
    'admin_driver_created'       => 'Admin created new driver',
    'admin_driver_updated'       => 'Admin updated driver information',
    'admin_driver_deleted'       => 'Admin deleted driver',
    'admin_driver_viewed'        => 'Admin viewed driver details',
    'admin_clients_list_viewed'  => 'Admin accessed clients list',
    'admin_routes_list_viewed'   => 'Admin accessed routes list',
    'admin_drivers_list_viewed'  => 'Admin accessed drivers list',

    // Audit Log Types
    'log_type_1'                 => 'Create',
    'log_type_2'                 => 'Update',
    'log_type_3'                 => 'Delete',
    'log_type_4'                 => 'View',
    'log_type_5'                 => 'Export',
    'log_type_6'                 => 'Import',
    'log_type_7'                 => 'Other',

    // Payment Messages
    'payment_created_successfully' => 'Payment created successfully',
    'payment_updated_successfully' => 'Payment updated successfully',
    'payment_deleted_successfully' => 'Payment deleted successfully',
    'payment_status_updated_successfully' => 'Payment status updated successfully',
    'invoice_generated_successfully' => 'Invoice generated successfully',
    'payments_exported_successfully' => 'Payments exported successfully',
    'bulk_status_updated_successfully' => 'Bulk status updated successfully',
    'cannot_delete_completed_payment' => 'Cannot delete completed payment',
    'cannot_modify_completed_payment' => 'Cannot modify completed payment',
    'subscription_required' => 'Subscription is required',
    'subscription_not_found' => 'Subscription not found',
    'amount_required' => 'Amount is required',
    'amount_numeric' => 'Amount must be a number',
    'amount_min' => 'Amount must be at least 0',
    'amount_max' => 'Amount cannot exceed 999,999.99',
    'amount_exceeds_due' => 'Amount (:amount) exceeds due amount (:due)',
    'payment_date_required' => 'Payment date is required',
    'payment_date_date' => 'Payment date must be a valid date',
    'payment_date_past' => 'Payment date cannot be in the future',
    'payment_method_required' => 'Payment method is required',
    'payment_method_invalid' => 'Payment method must be cash, card, or bank_transfer',
    'payment_status_required' => 'Payment status is required',
    'payment_status_invalid' => 'Payment status must be pending, completed, or failed',
    'reference_number_exists' => 'Reference number already exists',
    'reference_number_max_length' => 'Reference number cannot exceed 255 characters',

    // Subscription Messages
    'subscription_created_successfully' => 'Subscription created successfully',
    'subscription_updated_successfully' => 'Subscription updated successfully',
    'subscription_deleted_successfully' => 'Subscription deleted successfully',
    'cannot_delete_subscription_with_payments' => 'Cannot delete subscription with payments',
    'client_required' => 'Client is required',
    'client_not_found' => 'Client not found',
    'route_required' => 'Route is required',
    'route_not_found' => 'Route not found',
    'route_at_capacity' => 'Route is at full capacity',
    'student_name_required' => 'Student name is required',
    'student_name_max_length' => 'Student name cannot exceed 255 characters',
    'subscription_type_required' => 'Subscription type is required',
    'subscription_type_invalid' => 'Subscription type must be monthly, term, or yearly',
    'price_required' => 'Price is required',
    'price_numeric' => 'Price must be a number',
    'price_min' => 'Price must be at least 0',
    'price_max' => 'Price cannot exceed 999,999.99',
    'price_mismatch' => 'Price (:price) does not match route price (:route_price)',
    'start_date_required' => 'Start date is required',
    'start_date_date' => 'Start date must be a valid date',
    'end_date_required' => 'End date is required',
    'end_date_date' => 'End date must be a valid date',
    'end_date_after_start' => 'End date must be after start date',
    'subscription_status_required' => 'Subscription status is required',
    'subscription_status_invalid' => 'Subscription status must be active, inactive, or suspended',
    'subscription_overlap' => 'Client already has an active subscription for this period',
    'notes_max_length' => 'Notes cannot exceed 1000 characters',

    // Route Messages
    'route_created_successfully' => 'Route created successfully',
    'route_updated_successfully' => 'Route updated successfully',
    'route_deleted_successfully' => 'Route deleted successfully',
    'cannot_delete_route_with_subscriptions' => 'Cannot delete route with active subscriptions',
    'cannot_deactivate_route_with_active_subscriptions' => 'Cannot deactivate route with active subscriptions',
    'route_name_required' => 'Route name is required',
    'route_name_exists' => 'Route name already exists',
    'route_name_max_length' => 'Route name cannot exceed 255 characters',
    'from_area_required' => 'From area is required',
    'from_area_max_length' => 'From area cannot exceed 255 characters',
    'to_school_required' => 'To school is required',
    'to_school_max_length' => 'To school cannot exceed 255 characters',
    'pickup_time_required' => 'Pickup time is required',
    'pickup_time_format' => 'Pickup time must be in HH:MM format',
    'dropoff_time_required' => 'Dropoff time is required',
    'dropoff_time_format' => 'Dropoff time must be in HH:MM format',
    'dropoff_time_after_pickup' => 'Dropoff time must be after pickup time',
    'monthly_price_required' => 'Monthly price is required',
    'monthly_price_numeric' => 'Monthly price must be a number',
    'monthly_price_min' => 'Monthly price must be at least 0',
    'monthly_price_max' => 'Monthly price cannot exceed 999,999.99',
    'capacity_required' => 'Capacity is required',
    'capacity_integer' => 'Capacity must be a whole number',
    'capacity_min' => 'Capacity must be at least 1',
    'capacity_max' => 'Capacity cannot exceed 100',
    'capacity_below_current_students' => 'Capacity (:requested) cannot be less than current students (:current)',
    'driver_not_active' => 'Selected driver is not active',
    'driver_already_assigned' => 'Driver is already assigned to another route',
    'stops_array' => 'Stops must be an array',
    'stops_max_count' => 'Cannot have more than 20 stops',
    'stop_string' => 'Each stop must be text',
    'stop_max_length' => 'Each stop cannot exceed 255 characters',

    // Driver Messages
    'driver_created_successfully' => 'Driver created successfully',
    'driver_updated_successfully' => 'Driver updated successfully',
    'driver_deleted_successfully' => 'Driver deleted successfully',
    'cannot_delete_driver_with_active_routes' => 'Cannot delete driver with active route assignments',
    'cannot_deactivate_driver_with_active_routes' => 'Cannot deactivate driver with active route assignments',
    'driver_removed_from_route' => 'Driver removed from route successfully',
    'driver_suspended_successfully' => 'Driver suspended successfully',
    'driver_activated_successfully' => 'Driver activated successfully',
    'driver_name_required' => 'Driver name is required',
    'driver_name_max_length' => 'Driver name cannot exceed 255 characters',
    'phone_required' => 'Phone number is required',
    'phone_exists' => 'Phone number already exists',
    'phone_max_length' => 'Phone number cannot exceed 20 characters',
    'driver_email_invalid' => 'Email address is invalid',
    'driver_email_exists' => 'Email address already exists',
    'license_number_required' => 'License number is required',
    'license_number_exists' => 'License number already exists',
    'license_number_max_length' => 'License number cannot exceed 255 characters',
    'license_expiry_required' => 'License expiry date is required',
    'license_expiry_date' => 'License expiry must be a valid date',
    'license_expiry_future' => 'License expiry must be in the future',
    'license_expiry_too_far' => 'License expiry cannot be more than 10 years in the future',
    'national_id_required' => 'National ID is required',
    'national_id_exists' => 'National ID already exists',
    'national_id_max_length' => 'National ID cannot exceed 255 characters',
    'address_max_length' => 'Address cannot exceed 500 characters',
    'hire_date_required' => 'Hire date is required',
    'hire_date_date' => 'Hire date must be a valid date',
    'hire_date_past' => 'Hire date cannot be in the future',
    'hire_date_future' => 'Hire date cannot be in the future',
    'salary_numeric' => 'Salary must be a number',
    'salary_min' => 'Salary must be at least 0',
    'salary_max' => 'Salary cannot exceed 999,999.99',
    'status_required' => 'Status is required',
    'status_invalid' => 'Status must be active, inactive, or suspended',
    'vehicle_type_max_length' => 'Vehicle type cannot exceed 255 characters',
    'vehicle_model_max_length' => 'Vehicle model cannot exceed 255 characters',
    'vehicle_year_max_length' => 'Vehicle year cannot exceed 4 characters',
    'vehicle_plate_max_length' => 'Vehicle plate number cannot exceed 255 characters',
    'vehicle_color_max_length' => 'Vehicle color cannot exceed 255 characters',
    'vehicle_capacity_integer' => 'Vehicle capacity must be a whole number',
    'vehicle_capacity_min' => 'Vehicle capacity must be at least 1',
    'vehicle_capacity_max' => 'Vehicle capacity cannot exceed 100',
    'vehicle_notes_max_length' => 'Vehicle notes cannot exceed 1000 characters',
    'emergency_contact_name_max_length' => 'Emergency contact name cannot exceed 255 characters',
    'emergency_contact_phone_max_length' => 'Emergency contact phone cannot exceed 20 characters',
    'emergency_contact_relation_max_length' => 'Emergency contact relation cannot exceed 255 characters',

    // Security Messages
    'rate_limit_exceeded' => 'Too many requests. Please try again later.',
    'csrf_token_mismatch' => 'Security token mismatch. Please refresh and try again.',
    'invalid_signature' => 'Invalid request signature.',
    'request_timeout' => 'Request timeout. Please try again.',
    'file_too_large' => 'File size exceeds maximum allowed limit.',
    'invalid_file_type' => 'Invalid file type.',
    'upload_failed' => 'File upload failed.',
    'access_denied' => 'Access denied.',
    'session_expired' => 'Session expired. Please login again.',
    'account_suspended' => 'Account suspended. Contact administrator.',
    'ip_blocked' => 'Your IP address has been blocked.',
    'suspicious_activity' => 'Suspicious activity detected.',

    // System Messages
    'system_maintenance' => 'System under maintenance. Please try again later.',
    'database_error' => 'Database error occurred. Please contact support.',
    'server_error' => 'Server error occurred. Please try again later.',
    'service_unavailable' => 'Service temporarily unavailable.',
    'cache_cleared' => 'Cache cleared successfully.',
    'backup_created' => 'Backup created successfully.',
    'backup_restored' => 'Backup restored successfully.',
    'settings_updated' => 'Settings updated successfully.',
    'configuration_saved' => 'Configuration saved successfully.',

    // Custom Validation Messages
    'strong_password' => 'The :attribute must contain at least 8 characters with uppercase, lowercase, number, and special character.',
    'safe_filename' => 'The :attribute contains invalid characters.',
    'no_scripts' => 'The :attribute cannot contain script tags.',
    'no_sql_injection' => 'The :attribute contains potentially dangerous content.',

    // Dashboard specific
    'welcome_back' => 'Welcome back',
    'business_management' => 'Business Management',
    'subscription_management' => 'Subscription Management',
    'active_clients' => 'Active Clients',
    'available_routes' => 'Available Routes',
    'active_drivers' => 'Active Drivers',
    'this_month' => 'This Month',
    'switching_language' => 'Switching Language',
    'loading' => 'Loading',
    'close' => 'Close',
    'logo' => 'Logo',
    'admin' => 'Admin',
    'profile' => 'Profile',
    'database_status' => 'Database Status',
    'cache_status' => 'Cache Status',
    'storage_usage' => 'Storage Usage',
    'memory_usage' => 'Memory Usage',
    'queue_status' => 'Queue Status',
    'system_uptime' => 'System Uptime',
    'active_routes' => 'Active Routes',
    'total' => 'Total',
    'manage_routes' => 'Manage Routes',
    'manage_drivers' => 'Manage Drivers',
    'monthly_revenue' => 'Monthly Revenue',
    'vs_last_month' => 'vs Last Month',
    'on_leave' => 'On Leave',
    'view_all' => 'View All',
    'exporting' => 'Exporting',
    'new_clients' => 'New Clients',
    'revenue_trend' => 'Revenue Trend',
    'client_growth' => 'Client Growth',
    'subscription_status' => 'Subscription Status',
    'driver_status' => 'Driver Status',
    'total_students' => 'Total Students',
    'new_this_month' => 'New This Month',
    'logged_in_today' => 'Logged In Today',
    'view_financial' => 'View Financial',
    'system_health' => 'System Health',

    // Admin Pages - General
    'driver_management' => 'Driver Management',
    'route_management' => 'Route Management',
    'financial_management' => 'Financial Management',
    'subscription_management' => 'Subscription Management',
    'payment_management' => 'Payment Management',
    'add_driver' => 'Add Driver',
    'add_route' => 'Add Route',
    'add_subscription' => 'Add Subscription',
    'driver_list' => 'Driver List',
    'route_list' => 'Route List',

    // Form and Validation
    'please_fix_errors' => 'Please fix the following errors:',
    'subscription_info' => 'Subscription Information',
    'back_to_subscriptions' => 'Back to Subscriptions',
    'validation_errors' => 'Validation Errors',
    'role_information' => 'Role Information',
    'back_to_roles' => 'Back to Roles',
    'no_permissions_description' => 'No permissions available for assignment.',
    'no_permissions_selected_confirm' => 'No permissions selected. Are you sure you want to create this role?',

    // Financial Reports
    'financial_reports' => 'Financial Reports',
    'back_to_dashboard' => 'Back to Dashboard',
    'filters' => 'Filters',
    'total_completed' => 'Total Completed',
    'pending_amount' => 'Pending Amount',
    'collection_rate' => 'Collection Rate',
    'completion_rate' => 'Completion Rate',
    'total_payments' => 'Total Payments',
    'selected_period' => 'Selected Period',
    'subscription_list' => 'Subscription List',

    // Client Management
    'add_new_client' => 'Add New Client',
    'back_to_clients' => 'Back to Clients',

    // Statistics Cards
    'total_drivers' => 'Total Drivers',
    'active_drivers' => 'Active Drivers',
    'suspended_drivers' => 'Suspended Drivers',
    'total_routes' => 'Total Routes',
    'active_routes' => 'Active Routes',
    'total_subscriptions' => 'Total Subscriptions',
    'active_subscriptions' => 'Active Subscriptions',
    'expired_subscriptions' => 'Expired Subscriptions',
    'pending_subscriptions' => 'Pending Subscriptions',
    'monthly_revenue' => 'Monthly Revenue',
    'total_revenue' => 'Total Revenue',
    'pending_payments' => 'Pending Payments',
    'completed_payments' => 'Completed Payments',

    // Actions and Buttons
    'add_new_driver' => 'Add New Driver',
    'add_new_route' => 'Add New Route',
    'add_new_subscription' => 'Add New Subscription',
    'view_details' => 'View Details',
    'edit_driver' => 'Edit Driver',
    'edit_route' => 'Edit Route',
    'edit_subscription' => 'Edit Subscription',
    'suspend_driver' => 'Suspend Driver',
    'activate_driver' => 'Activate Driver',
    'assign_route' => 'Assign Route',
    'unassign_route' => 'Unassign Route',

    // Status Labels
    'driver_status' => 'Driver Status',
    'route_status' => 'Route Status',
    'subscription_status' => 'Subscription Status',
    'payment_status' => 'Payment Status',
    'license_status' => 'License Status',
    'expiring_soon' => 'Expiring Soon',
    'expired' => 'Expired',
    'valid' => 'Valid',
    'unassigned_drivers' => 'Unassigned Drivers',
    'assigned_drivers' => 'Assigned Drivers',

];
