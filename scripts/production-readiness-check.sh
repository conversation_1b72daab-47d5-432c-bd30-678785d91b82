#!/bin/bash

# Royal Transit Production Readiness Check Script
# This script performs various checks to ensure the application is production-ready

echo "🚀 Royal Transit Production Readiness Check"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    echo -e "${RED}Error: Please run this script from the Laravel project root directory${NC}"
    exit 1
fi

echo ""
echo "1. Environment Configuration Check"
echo "=================================="

# Check if .env file exists
if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    # Check critical environment variables
    if grep -q "APP_ENV=production" .env; then
        print_status 0 "APP_ENV is set to production"
    else
        print_warning "APP_ENV is not set to production"
    fi
    
    if grep -q "APP_DEBUG=false" .env; then
        print_status 0 "APP_DEBUG is set to false"
    else
        print_warning "APP_DEBUG should be false in production"
    fi
    
    if grep -q "APP_KEY=" .env && ! grep -q "APP_KEY=$" .env; then
        print_status 0 "APP_KEY is configured"
    else
        print_status 1 "APP_KEY is not configured"
    fi
else
    print_status 1 ".env file does not exist"
fi

echo ""
echo "2. Database Configuration Check"
echo "==============================="

# Check database connection
php artisan migrate:status > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Database connection successful"
    
    # Check if migrations are up to date
    PENDING_MIGRATIONS=$(php artisan migrate:status | grep -c "Pending")
    if [ $PENDING_MIGRATIONS -eq 0 ]; then
        print_status 0 "All migrations are up to date"
    else
        print_warning "$PENDING_MIGRATIONS pending migrations found"
    fi
else
    print_status 1 "Database connection failed"
fi

echo ""
echo "3. Security Configuration Check"
echo "==============================="

# Check if security middleware is registered
if grep -q "SecurityHeaders" app/Http/Kernel.php; then
    print_status 0 "Security headers middleware is registered"
else
    print_warning "Security headers middleware not found"
fi

# Check if rate limiting is configured
if grep -q "enhanced.throttle" app/Http/Kernel.php; then
    print_status 0 "Enhanced rate limiting is configured"
else
    print_warning "Enhanced rate limiting not configured"
fi

# Check if HTTPS is enforced in production
if grep -q "SESSION_SECURE_COOKIES=true" .env; then
    print_status 0 "Secure cookies are enabled"
else
    print_warning "Secure cookies should be enabled in production"
fi

echo ""
echo "4. File Permissions Check"
echo "========================="

# Check storage directory permissions
if [ -w "storage" ]; then
    print_status 0 "Storage directory is writable"
else
    print_status 1 "Storage directory is not writable"
fi

# Check bootstrap/cache directory permissions
if [ -w "bootstrap/cache" ]; then
    print_status 0 "Bootstrap cache directory is writable"
else
    print_status 1 "Bootstrap cache directory is not writable"
fi

echo ""
echo "5. Cache and Optimization Check"
echo "==============================="

# Check if config is cached
if [ -f "bootstrap/cache/config.php" ]; then
    print_status 0 "Configuration is cached"
else
    print_warning "Configuration should be cached in production"
    print_info "Run: php artisan config:cache"
fi

# Check if routes are cached
if [ -f "bootstrap/cache/routes-v7.php" ]; then
    print_status 0 "Routes are cached"
else
    print_warning "Routes should be cached in production"
    print_info "Run: php artisan route:cache"
fi

# Check if views are cached
if [ -d "storage/framework/views" ] && [ "$(ls -A storage/framework/views)" ]; then
    print_status 0 "Views are compiled"
else
    print_warning "Views should be compiled in production"
    print_info "Run: php artisan view:cache"
fi

echo ""
echo "6. Dependencies and Security Check"
echo "=================================="

# Check if composer dependencies are optimized
if [ -f "vendor/composer/autoload_classmap.php" ]; then
    print_status 0 "Composer autoloader is optimized"
else
    print_warning "Composer autoloader should be optimized"
    print_info "Run: composer install --optimize-autoloader --no-dev"
fi

# Check for development dependencies in production
if [ -f "composer.lock" ]; then
    DEV_DEPS=$(composer show --no-dev 2>/dev/null | wc -l)
    ALL_DEPS=$(composer show 2>/dev/null | wc -l)
    if [ $DEV_DEPS -eq $ALL_DEPS ]; then
        print_status 0 "No development dependencies in production"
    else
        print_warning "Development dependencies found in production"
        print_info "Run: composer install --no-dev"
    fi
fi

echo ""
echo "7. Log Files Check"
echo "=================="

# Check if log directory exists and is writable
if [ -d "storage/logs" ] && [ -w "storage/logs" ]; then
    print_status 0 "Log directory exists and is writable"
    
    # Check log file sizes
    LOG_SIZE=$(du -sh storage/logs 2>/dev/null | cut -f1)
    print_info "Current log directory size: $LOG_SIZE"
    
    # Check for large log files
    LARGE_LOGS=$(find storage/logs -name "*.log" -size +100M 2>/dev/null | wc -l)
    if [ $LARGE_LOGS -gt 0 ]; then
        print_warning "$LARGE_LOGS log files are larger than 100MB"
        print_info "Consider log rotation or cleanup"
    fi
else
    print_status 1 "Log directory issues detected"
fi

echo ""
echo "8. Queue and Job Configuration"
echo "=============================="

# Check if queue driver is configured
if grep -q "QUEUE_CONNECTION=redis\|QUEUE_CONNECTION=database" .env; then
    print_status 0 "Production queue driver is configured"
else
    print_warning "Queue driver should be redis or database in production"
fi

echo ""
echo "9. Final Recommendations"
echo "========================"

print_info "Production Deployment Checklist:"
echo "  □ Run: php artisan config:cache"
echo "  □ Run: php artisan route:cache"
echo "  □ Run: php artisan view:cache"
echo "  □ Run: composer install --optimize-autoloader --no-dev"
echo "  □ Set proper file permissions (755 for directories, 644 for files)"
echo "  □ Configure SSL/TLS certificate"
echo "  □ Set up log rotation"
echo "  □ Configure backup strategy"
echo "  □ Set up monitoring and alerting"
echo "  □ Test all critical functionality"

echo ""
echo "🎉 Production readiness check completed!"
echo "Review any warnings above before deploying to production."
