<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\UserTypes;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Carbon\Carbon;

class ProductionSeeder extends Seeder
{
    /**
     * Run the database seeds for production environment.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createUserTypes();
            $this->createPermissions();
            $this->createRoles();
            $this->createSuperAdmin();
            $this->createDefaultSettings();
            $this->createSampleData();
        });

        $this->command->info('Production database seeded successfully!');
    }

    /**
     * Create user types
     */
    private function createUserTypes()
    {
        $userTypes = [
            ['id' => 1, 'name' => 'Admin', 'description' => 'System Administrator'],
            ['id' => 2, 'name' => 'Client', 'description' => 'Client User'],
            ['id' => 3, 'name' => 'Driver', 'description' => 'Driver User'],
            ['id' => 4, 'name' => 'Staff', 'description' => 'Staff Member'],
        ];

        foreach ($userTypes as $type) {
            UserTypes::firstOrCreate(['id' => $type['id']], $type);
        }

        $this->command->info('User types created.');
    }

    /**
     * Create permissions
     */
    private function createPermissions()
    {
        $this->call(EnhancedPermissionSeeder::class);
        $this->command->info('Permissions created.');
    }

    /**
     * Create roles with proper permissions
     */
    private function createRoles()
    {
        // Super Admin Role
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
        $superAdminRole->syncPermissions(Permission::all());

        // Admin Role
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $adminPermissions = Permission::whereNotIn('name', [
            'force_delete users',
            'delete backups',
            'restore backups',
        ])->get();
        $adminRole->syncPermissions($adminPermissions);

        // Transport Manager Role
        $transportManagerRole = Role::firstOrCreate(['name' => 'transport_manager', 'guard_name' => 'web']);
        $transportPermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_clients%')
                  ->orWhere('name', 'like', '%admin_routes%')
                  ->orWhere('name', 'like', '%admin_drivers%')
                  ->orWhere('name', 'like', '%admin_subscriptions%')
                  ->orWhere('name', 'like', '%admin_students%')
                  ->orWhere('name', 'like', '%dashboard%');
        })->get();
        $transportManagerRole->syncPermissions($transportPermissions);

        // Financial Manager Role
        $financialManagerRole = Role::firstOrCreate(['name' => 'financial_manager', 'guard_name' => 'web']);
        $financialPermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_payments%')
                  ->orWhere('name', 'like', '%admin_financial%')
                  ->orWhere('name', 'like', '%admin_reports%')
                  ->orWhere('name', 'like', '%dashboard%')
                  ->orWhere('name', 'read admin_clients')
                  ->orWhere('name', 'read admin_subscriptions');
        })->get();
        $financialManagerRole->syncPermissions($financialPermissions);

        // Client Service Role
        $clientServiceRole = Role::firstOrCreate(['name' => 'client_service', 'guard_name' => 'web']);
        $clientServicePermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_clients%')
                  ->orWhere('name', 'like', '%admin_students%')
                  ->orWhere('name', 'like', '%admin_subscriptions%')
                  ->orWhere('name', 'read admin_payments')
                  ->orWhere('name', 'view dashboard');
        })->whereNotIn('name', [
            'delete admin_clients',
            'delete admin_students',
        ])->get();
        $clientServiceRole->syncPermissions($clientServicePermissions);

        $this->command->info('Roles created with permissions.');
    }

    /**
     * Create super admin user
     */
    private function createSuperAdmin()
    {
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'phone' => '01000000000',
                'password' => Hash::make(config('client.default_user_password', 'RoyalTransit2024!')),
                'email_verified_at' => now(),
                'user_type_id' => 1,
                'lang' => 'ar',
            ]
        );

        $superAdmin->assignRole('super_admin');

        $this->command->info('Super admin created: <EMAIL>');
    }

    /**
     * Create default system settings
     */
    private function createDefaultSettings()
    {
        // This would typically involve creating settings records
        // For now, we'll just log that settings would be created
        $this->command->info('Default settings configured.');
    }

    /**
     * Create sample data for demonstration (only in non-production)
     */
    private function createSampleData()
    {
        if (app()->environment('production')) {
            $this->command->info('Skipping sample data in production environment.');
            return;
        }

        // Create sample admin users
        $this->createSampleAdminUsers();
        
        // Create sample areas and schools
        $this->createSampleAreasAndSchools();

        $this->command->info('Sample data created for development/testing.');
    }

    /**
     * Create sample admin users
     */
    private function createSampleAdminUsers()
    {
        $adminUsers = [
            [
                'name' => 'Transport Manager',
                'email' => '<EMAIL>',
                'phone' => '01111111111',
                'role' => 'transport_manager',
            ],
            [
                'name' => 'Financial Manager',
                'email' => '<EMAIL>',
                'phone' => '01222222222',
                'role' => 'financial_manager',
            ],
            [
                'name' => 'Client Service',
                'email' => '<EMAIL>',
                'phone' => '01333333333',
                'role' => 'client_service',
            ],
        ];

        foreach ($adminUsers as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'phone' => $userData['phone'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'user_type_id' => 1,
                    'lang' => 'ar',
                ]
            );

            $user->assignRole($userData['role']);
        }
    }

    /**
     * Create sample areas and schools
     */
    private function createSampleAreasAndSchools()
    {
        // Sample areas
        $areas = [
            'المعادي',
            'مدينة نصر',
            'الزمالك',
            'المهندسين',
            'الدقي',
            'الجيزة',
            'حلوان',
            'المقطم',
            'التجمع الخامس',
            'الشيخ زايد',
        ];

        // Sample schools
        $schools = [
            'مدرسة النيل الدولية',
            'المدرسة الأمريكية',
            'مدرسة المستقبل',
            'المدرسة الفرنسية',
            'مدرسة الشويفات',
            'مدرسة الجزيرة',
            'المدرسة الألمانية',
            'مدرسة الأورمان',
            'مدرسة المنارة',
            'المدرسة الحديثة',
        ];

        // These would typically be stored in dedicated tables
        // For now, we'll just log that they would be created
        $this->command->info('Sample areas and schools data prepared.');
    }
}
