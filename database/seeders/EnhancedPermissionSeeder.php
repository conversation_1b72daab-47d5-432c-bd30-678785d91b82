<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

class EnhancedPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define all permission categories and their actions
        $permissionCategories = [
            // Core Admin Permissions
            'dashboard' => ['view'],
            'analytics' => ['view'],
            
            // User Management
            'users' => ['create', 'read', 'update', 'delete', 'restore', 'force_delete'],
            'roles' => ['create', 'read', 'update', 'delete', 'assign', 'revoke'],
            'permissions' => ['create', 'read', 'update', 'delete', 'assign', 'revoke'],
            
            // Client Management
            'admin_clients' => ['create', 'read', 'update', 'delete', 'view', 'export', 'import'],
            'admin_students' => ['create', 'read', 'update', 'delete', 'view'],
            
            // Transport Management
            'admin_routes' => ['create', 'read', 'update', 'delete', 'view', 'assign_driver', 'manage_capacity'],
            'admin_drivers' => ['create', 'read', 'update', 'delete', 'view', 'suspend', 'activate', 'assign_route'],
            
            // Subscription Management
            'admin_subscriptions' => ['create', 'read', 'update', 'delete', 'view', 'activate', 'suspend', 'renew'],
            
            // Payment Management
            'admin_payments' => ['create', 'read', 'update', 'delete', 'view', 'process', 'refund', 'generate_invoice'],
            
            // Financial Management
            'admin_financial' => ['view', 'update', 'export_reports', 'manage_expenses'],
            'admin_reports' => ['view', 'generate', 'export', 'schedule'],
            
            // System Management
            'system_settings' => ['read', 'update'],
            'translations' => ['create', 'read', 'update', 'delete', 'export', 'import'],
            'audit_logs' => ['read', 'export', 'delete'],
            'backups' => ['create', 'read', 'restore', 'delete'],
            
            // API Management
            'api_access' => ['read', 'manage_tokens'],
            
            // Notification Management
            'notifications' => ['create', 'read', 'update', 'delete', 'send'],
        ];

        // Create permissions
        $permissions = [];
        foreach ($permissionCategories as $category => $actions) {
            foreach ($actions as $action) {
                $permissionName = $action . ' ' . $category;
                $permissions[] = [
                    'name' => $permissionName,
                    'guard_name' => 'web',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }
        }

        // Insert permissions (ignore duplicates)
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        $this->command->info('Enhanced permissions created successfully!');
        
        // Update existing roles with new permissions
        $this->updateRolePermissions();
    }

    /**
     * Update existing roles with appropriate permissions
     */
    private function updateRolePermissions()
    {
        // Super Admin - gets all permissions
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);

        // Admin - comprehensive access but not system-critical operations
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $adminPermissions = Permission::whereNotIn('name', [
            'force_delete users',
            'delete backups',
            'restore backups',
            'delete audit_logs',
        ])->get();
        $adminRole->syncPermissions($adminPermissions);

        // Transport Manager - transport operations focused
        $transportManagerRole = Role::firstOrCreate(['name' => 'transport_manager', 'guard_name' => 'web']);
        $transportPermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_clients%')
                  ->orWhere('name', 'like', '%admin_routes%')
                  ->orWhere('name', 'like', '%admin_drivers%')
                  ->orWhere('name', 'like', '%admin_subscriptions%')
                  ->orWhere('name', 'like', '%admin_students%')
                  ->orWhere('name', 'like', '%dashboard%')
                  ->orWhere('name', 'like', '%analytics%');
        })->whereNotIn('name', [
            'delete admin_clients',
            'force_delete admin_clients',
        ])->get();
        $transportManagerRole->syncPermissions($transportPermissions);

        // Financial Manager - financial operations focused
        $financialManagerRole = Role::firstOrCreate(['name' => 'financial_manager', 'guard_name' => 'web']);
        $financialPermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_payments%')
                  ->orWhere('name', 'like', '%admin_financial%')
                  ->orWhere('name', 'like', '%admin_reports%')
                  ->orWhere('name', 'like', '%dashboard%')
                  ->orWhere('name', 'like', '%analytics%')
                  ->orWhere('name', 'read admin_clients')
                  ->orWhere('name', 'read admin_subscriptions');
        })->get();
        $financialManagerRole->syncPermissions($financialPermissions);

        // Operator - read-only access with basic operations
        $operatorRole = Role::firstOrCreate(['name' => 'operator', 'guard_name' => 'web']);
        $operatorPermissions = Permission::where(function($query) {
            $query->where('name', 'like', 'read %')
                  ->orWhere('name', 'like', 'view %')
                  ->orWhere('name', 'update admin_subscriptions')
                  ->orWhere('name', 'create admin_payments');
        })->whereNotIn('name', [
            'read system_settings',
            'read audit_logs',
            'read backups',
        ])->get();
        $operatorRole->syncPermissions($operatorPermissions);

        // Client Service - client-focused operations
        $clientServiceRole = Role::firstOrCreate(['name' => 'client_service', 'guard_name' => 'web']);
        $clientServicePermissions = Permission::where(function($query) {
            $query->where('name', 'like', '%admin_clients%')
                  ->orWhere('name', 'like', '%admin_students%')
                  ->orWhere('name', 'like', '%admin_subscriptions%')
                  ->orWhere('name', 'read admin_payments')
                  ->orWhere('name', 'view admin_payments')
                  ->orWhere('name', 'read admin_routes')
                  ->orWhere('name', 'view dashboard');
        })->whereNotIn('name', [
            'delete admin_clients',
            'delete admin_students',
            'delete admin_subscriptions',
        ])->get();
        $clientServiceRole->syncPermissions($clientServicePermissions);

        $this->command->info('Role permissions updated successfully!');
    }
}
