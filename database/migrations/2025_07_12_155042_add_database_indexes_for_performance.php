<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add indexes for users table
        Schema::table('users', function (Blueprint $table) {
            $table->index(['email', 'deleted_at'], 'users_email_deleted_at_index');
            $table->index(['phone', 'deleted_at'], 'users_phone_deleted_at_index');
            $table->index(['user_type_id', 'deleted_at'], 'users_type_deleted_at_index');
            $table->index(['created_at'], 'users_created_at_index');
            $table->index(['email_verified_at'], 'users_verified_at_index');
        });

        // Add indexes for clients table
        Schema::table('clients', function (Blueprint $table) {
            $table->index(['user_id'], 'clients_user_id_index');
            $table->index(['area'], 'clients_area_index');
            $table->index(['client_type'], 'clients_type_index');
            $table->index(['created_at'], 'clients_created_at_index');
        });

        // Add indexes for students table
        Schema::table('students', function (Blueprint $table) {
            $table->index(['client_id'], 'students_client_id_index');
            $table->index(['school'], 'students_school_index');
            $table->index(['education_stage'], 'students_education_stage_index');
            $table->index(['class_level'], 'students_class_level_index');
        });

        // Add indexes for drivers table
        Schema::table('drivers', function (Blueprint $table) {
            $table->index(['status', 'deleted_at'], 'drivers_status_deleted_at_index');
            $table->index(['license_expiry'], 'drivers_license_expiry_index');
            $table->index(['hire_date'], 'drivers_hire_date_index');
            $table->index(['vehicle_type'], 'drivers_vehicle_type_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop indexes for users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_email_deleted_at_index');
            $table->dropIndex('users_phone_deleted_at_index');
            $table->dropIndex('users_type_deleted_at_index');
            $table->dropIndex('users_created_at_index');
            $table->dropIndex('users_verified_at_index');
        });

        // Drop indexes for clients table
        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex('clients_user_id_index');
            $table->dropIndex('clients_area_index');
            $table->dropIndex('clients_type_index');
            $table->dropIndex('clients_created_at_index');
        });

        // Drop indexes for students table
        Schema::table('students', function (Blueprint $table) {
            $table->dropIndex('students_client_id_index');
            $table->dropIndex('students_school_index');
            $table->dropIndex('students_education_stage_index');
            $table->dropIndex('students_class_level_index');
        });

        // Drop indexes for drivers table
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropIndex('drivers_status_deleted_at_index');
            $table->dropIndex('drivers_license_expiry_index');
            $table->dropIndex('drivers_hire_date_index');
            $table->dropIndex('drivers_vehicle_type_index');
        });
    }
};
